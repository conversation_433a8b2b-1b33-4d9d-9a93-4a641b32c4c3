#!/usr/bin/env python

from __future__ import print_function
from collections import OrderedDict
import argparse
import json
import math
import sys

try:
    from cassandra.cluster import Cluster
    from cassandra.auth import PlainTextAuthProvider
except ImportError:
    print("Failed to load the cassandra driver. If you are using python from homebrew (recommmended, I suggest "
          "you run `brew install python`), do `pip install cassandra-driver`. Otherwise, with the Mac OSX default "
          "python, you need to install cassandra-driver, but you're on your own. On Debian, run `apt-get install "
          "python-cassandra`.")
    raise


# Seller-specific values, these can be set on command line
KEYSPACE_NAME = "seller"
KEYSPACE_DURABLE_WRITES = True

# The tables to create. This can be specified on command line.
#
# If you only want the default values, specify it like so:
#    'table_name': {},
#
# Otherwise, specify the parameters that will not be default:
#    'table_name': {
#         'gc_grace_seconds': 19200
#    }
TABLES = {
    'syi': {},
    'checkout': {},
    'user': {},
    'mad_filter': {},
    'secure_token': {},
    'open_id': {},
    'flash_map': {},
    'posting_rate': {}
}


# All code below this line should be generic

class TableDict(dict):
    """ A dict which falls back to our default values if not present
    """
    defaults = {
        'key': 'text',
        'value': 'text',
        'primary_key': 'key',
        'gc_grace_seconds': 86400,
    }

    def __init__(self, *args, **kwargs):
        super(TableDict, self).__init__(*args, **kwargs)

    def get(self, key, *args):
        """ Behave like normal dict.get()

        ...but return the default above if no default specified
        """
        if args:
            default = args[0]
        else:
            default = self.defaults.get(key)
        return super(TableDict, self).get(key, default)


def eprint(*args, **kwargs):
    print(*args, file=sys.stderr, **kwargs)


def calculate_repl_factor(node_count):
    """
    Determine a suitable value to use for the replica count based on the cluster size

    `(n+1) log 2` rounded (where n is the number of nodes) gives a pretty good approximation:

       +-------+----------+
       | Nodes | Replicas |
       |-------+----------|
       | 1     | 1        |
       | 2-4   | 2        |
       | 5-10  | 3        |
       | 11+   | 4        |
       +------------------+
    """
    return int(round(
        math.log((node_count + 1), 2),
        1
    ))


def create_keyspace(name, repl_factor, durable_writes=True):
    """ Create a keyspace
    """

    global session

    repl_config = OrderedDict({'class': 'NetworkTopologyStrategy'})
    for dc in data_centers:
        repl_config[dc] = repl_factor

    eprint("Creating keyspace '{}', with replication factor {}".format(name, repl_factor))
    query = "CREATE KEYSPACE IF NOT EXISTS {name} "\
            "WITH replication = {repl_config} "\
            "AND durable_writes = {durable_writes};".format(
                name=name,
                repl_config=json.dumps(repl_config).replace('"', "'"),
                durable_writes="true" if durable_writes else "false"
            )
    eprint(query)
    session.execute(query)


def create_table(keyspace, name, key, value, primary_key, gc_grace_seconds):
    """ Create a table
    """

    eprint("Creating table {}.{}".format(keyspace, name))
    values = {
        "keyspace": keyspace,
        "name": name,
        "key": key,
        "value": value,
        "primary_key": primary_key,
        "gc_grace_seconds": " WITH gc_grace_seconds = {}".format(gc_grace_seconds) if gc_grace_seconds else "",
    }

    query = "CREATE TABLE IF NOT EXISTS " \
            "{keyspace}.{name} (key {key}, value {value}, PRIMARY KEY({primary_key}))" \
            "{gc_grace_seconds};".format(**values)
    eprint(query)
    session.execute(query)


def create_tables(keyspace, tables):
    """ Parse the dict to create the tables

    Tables should be a dict of table params
    """

    global session
    session.set_keyspace(keyspace)

    for name, table_params in tables.items():
        # Convert dict to TableDict for defaults handling
        _table_params = TableDict(table_params)
        create_table(
            keyspace,
            name,
            _table_params.get('key'),
            _table_params.get('value'),
            _table_params.get('primary_key'),
            _table_params.get('gc_grace_seconds'),
        )

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Cassandra setup')

    parser.add_argument('--host', default='127.0.0.1',
                        help="Cassandra host to connect to",)
    parser.add_argument('--port', default=9042, type=int,
                        help="Port cassandra is listening on",)
    parser.add_argument('-u', '--username', required=False, default=None,
                        help="Username to authenticate to cassandra")
    parser.add_argument('-p', '--password', required=False, default="",
                        help="Password to authenticate to cassandra")
    parser.add_argument('-r', '--replication-factor', dest='repl_factor', metavar="NUM", type=int, required=False,
                        help="Set the replication factor for the created tables. If not specified, the number of "
                             "nodes is used to calculate an appropriate replication factor.")
    parser.add_argument('-k', '--keyspace-name', dest='keyspace', metavar="NAME", type=str,
                        help="Name of keyspace to create")
    parser.add_argument('-g', '--gc-grace-seconds', dest='gc_grace_seconds', metavar="SECONDS", type=int,
                        help="Value of gc_grace_seconds")
    parser.add_argument('--durable-writes', dest='durable_writes', action='store_true',
                        help='Set keyspace durable_writes to true')
    parser.add_argument('--no-durable-writes', dest='durable_writes', action='store_false',
                        help='Set keyspace durable_writes to false')
    parser.add_argument('--tables', metavar='TABLES', type=json.loads,
                        help="Dictionary of tables to create, see TABLES parameter in source for format")

    # Set defaults from globals at top of file
    parser.set_defaults(
        durable_writes=KEYSPACE_DURABLE_WRITES,
        keyspace=KEYSPACE_NAME,
        tables=TABLES,
    )
    args = parser.parse_args()

    if args.username:
        auth = PlainTextAuthProvider(
            username=args.username,
            password=args.password,
        )
    else:
        auth = None

    cluster = Cluster(
        contact_points=[args.host],
        port=args.port,
        auth_provider=auth,
    )

    session = cluster.connect()

    try:
        nodes = cluster.metadata.all_hosts()
        data_centers = set([node.datacenter for node in nodes])

        if len(data_centers) is 0:
            eprint("No other nodes/data centers found")
        else:
            eprint("Found {n} nodes in datacenters ['{d}']".format(n=len(nodes), d=', '.join(data_centers)))

        repl_factor = args.repl_factor or calculate_repl_factor(len(nodes))

        create_keyspace(
            args.keyspace,
            repl_factor,
            durable_writes=args.durable_writes,
        )

        create_tables(args.keyspace, args.tables)

    finally:
        session.shutdown()

    eprint("Done.")
