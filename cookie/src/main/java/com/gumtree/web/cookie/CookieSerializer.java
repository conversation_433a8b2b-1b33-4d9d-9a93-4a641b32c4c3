package com.gumtree.web.cookie;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import org.apache.commons.codec.binary.Base64;

import java.util.HashMap;
import java.util.Map;

public final class CookieSerializer {

    private static final Splitter FIELD_SPLITTER = Splitter.on("|").omitEmptyStrings().trimResults();
    private static final Joiner.MapJoiner FIELD_PAIR_JOINER = Joiner.on('|').withKeyValueSeparator(":");

    private CookieSerializer() {
    }

    public static Map<String, String> deserializeCookieMap(String cookieValue) {
        Map<String, String> map = new HashMap<>();

        if (cookieValue != null) {
            for (String field : FIELD_SPLITTER.split(cookieValue)) {
                String[] pair = field.split(":");
                if (pair.length == 2) {
                    String key = pair[0];
                    String value = deserialize(pair[1]);
                    map.put(key, value);
                }
            }
        }

        return map;
    }

    public static String serializeCookieMap(Map<String, String> map) {
        return FIELD_PAIR_JOINER
                .join(Maps.transformValues(map, input -> {
                    if (input != null) {
                        return serialize(input);
                    } else {
                        return new String(Base64.encodeBase64("".getBytes()));
                    }
                }));
    }

    public static String serialize(String str) {
        if (str != null) {
            return new String(Base64.encodeBase64(str.getBytes()));
        } else {
            return serialize("");
        }
    }

    public static String deserialize(String str) {
        return new String(Base64.decodeBase64(str.getBytes()));
    }
}
