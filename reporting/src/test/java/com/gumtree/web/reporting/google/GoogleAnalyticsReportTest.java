package com.gumtree.web.reporting.google;

import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsCustomVar;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsElementTrackEvent;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsNamedTrackEvent;
import com.gumtree.web.reporting.google.ga.impl.GoogleAnalyticsReport;
import com.gumtree.zeno.core.domain.PageType;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static com.gumtree.domain.category.Categories.ALL;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.sameInstance;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class GoogleAnalyticsReportTest {

    private GoogleAnalyticsReport report;

    @Before
    public void init() {
        report = new GoogleAnalyticsReport();
    }

    @Test
    public void countyIsNoLocationWhenLocationIsNull() {
        GoogleAnalyticsReportBuilder builder = report.county(null);
        assertThat(report.getCounty(), equalTo("No Location"));
        assertThat(builder, sameInstance((GoogleAnalyticsReportBuilder) report));
    }

    @Test
    public void countyIsUKWhenLocationIsUnitedKingdom() {
        Location location = mock(Location.class);
        when(location.getName()).thenReturn(Location.UK);
        GoogleAnalyticsReportBuilder builder = report.county(location);
        assertThat(report.getCounty(), equalTo("UK"));
        assertThat(builder, sameInstance((GoogleAnalyticsReportBuilder) report));
    }

    @Test
    public void countyIsDisplayNameFromLocation() {
        Location location = mock(Location.class);
        when(location.getDisplayName()).thenReturn("Surrey");
        GoogleAnalyticsReportBuilder builder = report.county(location);
        assertThat(report.getCounty(), equalTo("Surrey"));
        assertThat(builder, sameInstance((GoogleAnalyticsReportBuilder) report));
    }

    @Test
    public void pageTypeIsEmptyStringWhenPageTypeIsNull() {
        GoogleAnalyticsReportBuilder builder = report.pageType(null);
        assertThat(report.getPageType(), equalTo(""));
        assertThat(builder, sameInstance((GoogleAnalyticsReportBuilder) report));
    }

    @Test
    public void pageTypeIsBuiltCorrectlyWhenNotNull() {
        GoogleAnalyticsReportBuilder builder = report.pageType(PageType.R2SEmailSuccess);
        assertThat(report.getPageType(), equalTo("Reply To Seller"));
        assertThat(builder, sameInstance((GoogleAnalyticsReportBuilder) report));
    }

    @Test
    public void categoryIsNoCategoryWhenCategoryIsNull() {
        GoogleAnalyticsReportBuilder builder = report.category(null);
        assertThat(report.getCategory(), equalTo("No Category"));
        assertThat(builder, sameInstance((GoogleAnalyticsReportBuilder) report));
    }

    @Test
    public void categoryIsAllCategoriesWhenCategoryIsAll() {
        Category category = mock(Category.class);
        when(category.getSeoName()).thenReturn(ALL.getSeoName());
        GoogleAnalyticsReportBuilder builder = report.category(category);
        assertThat(report.getCategory(), equalTo("All Categories"));
        assertThat(builder, sameInstance((GoogleAnalyticsReportBuilder) report));
    }

    @Test
    public void categoryIsDisplayNameFromCategory() {
        Category category = mock(Category.class);
        when(category.getName()).thenReturn("Cars");
        GoogleAnalyticsReportBuilder builder = report.category(category);
        assertThat(report.getCategory(), equalTo("Cars"));
        assertThat(builder, sameInstance((GoogleAnalyticsReportBuilder) report));
    }

    @Test
    public void l1CategoryIsNoCategoryWhenCategoryIsNull() {
        GoogleAnalyticsReportBuilder builder = report.l1Category(null);
        assertThat(report.getL1Category(), equalTo("No Category"));
        assertThat(builder, sameInstance((GoogleAnalyticsReportBuilder) report));
    }

    @Test
    public void l1CategoryIsAllCategoriesWhenCategoryIsAll() {
        Category category = mock(Category.class);
        when(category.getSeoName()).thenReturn(ALL.getSeoName());
        GoogleAnalyticsReportBuilder builder = report.l1Category(category);
        assertThat(report.getL1Category(), equalTo("All Categories"));
        assertThat(builder, sameInstance((GoogleAnalyticsReportBuilder) report));
    }

    @Test
    public void l1CategoryIsDisplayNameFromCategory() {
        Category category = mock(Category.class);
        when(category.getName()).thenReturn("Cars");
        GoogleAnalyticsReportBuilder builder = report.l1Category(category);
        assertThat(report.getL1Category(), equalTo("Cars"));
        assertThat(builder, sameInstance((GoogleAnalyticsReportBuilder) report));
    }

    @Test
    public void testDefaults() {
        assertThat(report.getL1Category(), equalTo("No Category"));
        assertThat(report.getCategory(), equalTo("No Category"));
        assertThat(report.getCounty(), equalTo("No Location"));
        assertThat(report.getPageType(), equalTo(""));
    }

    @Test
    public void addCustomVar(){
        GoogleAnalyticsCustomVar customVar = mock(GoogleAnalyticsCustomVar.class);
        report.addCustomVar(customVar);
        assertThat(report.getCustomVars(), hasSize(1));
        assertThat(report.getCustomVars().get(0), is(customVar));
    }

    @Test
    public void notAddEmptyCustomVar(){
        report.addCustomVar(null);
        assertThat(report.getCustomVars(), empty());
    }

    @Test
    public void addElementTrackEvent(){
        GoogleAnalyticsElementTrackEvent trackEvent = mock(GoogleAnalyticsElementTrackEvent.class);
        report.addTrackEvent(trackEvent);
        List<GoogleAnalyticsElementTrackEvent> events = Lists.newArrayList(report.getElementTrackEvents());
        assertThat(events, hasSize(1));
        assertThat(events.get(0), is(trackEvent));
    }

    @Test
    public void notAddEmptyElementTrackEvent(){
        report.addTrackEvent(null);
        assertThat(Lists.newArrayList(report.getElementTrackEvents()), empty());
    }

    @Test
    public void addNamedTrackEvent(){
        GoogleAnalyticsNamedTrackEvent trackEvent = mock(GoogleAnalyticsNamedTrackEvent.class);
        report.addTrackEvent(trackEvent);
        List<GoogleAnalyticsNamedTrackEvent> events = Lists.newArrayList(report.getNamedTrackEvents());
        assertThat(events, hasSize(1));
        assertThat(events.get(0), is(trackEvent));
    }

    @Test
    public void notAddEmptyNamedTrackEvent(){
        report.addTrackEvent(null);
        assertThat(Lists.newArrayList(report.getNamedTrackEvents()), empty());
    }
}
