package com.gumtree.web.reporting.google.ga.events.impl;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.zeno.core.domain.PageType;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class RegisterCvBeginTest {

    @Test
    public void checkValues() {
        ThirdPartyRequestContext ctx = mock(ThirdPartyRequestContext.class);
        Category category = mock(Category.class);
        Location location = mock(Location.class);
        when(ctx.getPageType()).thenReturn(PageType.PostAdForm);
        when(ctx.getCategory()).thenReturn(category);
        when(ctx.getLocation()).thenReturn(location);
        when(category.getId()).thenReturn(23L);
        when(location.getId()).thenReturn(42);

        RegisterCvBegin event = new RegisterCvBegin(ctx);

        assertThat(event.getAction(), equalTo("RegisterCVBegin"));
        assertThat(event.getBindEvent(), equalTo("click"));
        assertThat(event.getBindSelector(), equalTo("[ga-event=\\'work-wanted-cv-db\\']"));
        assertThat(event.getCategory(), equalTo("Post Ad Form"));
    }

}
