package com.gumtree.web.reporting.google.ga.impl;

import com.gumtree.common.test.hamcrest.Answers;
import com.gumtree.domain.advert.Advert;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import org.junit.Before;
import org.junit.Test;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class AbstractGooogleAnalyticsConfigurerTest {


    private ThirdPartyRequestContext<Advert> requestContext;
    private AbstractGooogleAnalyticsConfigurer configurer;
    private GoogleAnalyticsTrackEventLabelBuilder builder;

    @Before
    public void setUp(){
        configurer = createConfigurer();
        requestContext = mock(ThirdPartyRequestContext.class);
        builder = mock(GoogleAnalyticsTrackEventLabelBuilder.class, Answers.fluentInterfaceAnswer());
    }

    @Test
    public void setLCategories(){
        Category category1 = mock(Category.class);
        Category category2 = mock(Category.class);
        when(category1.getId()).thenReturn(12L);
        when(category2.getId()).thenReturn(34L);
        when(requestContext.getCategory(1)).thenReturn(category1);
        when(requestContext.getCategory(2)).thenReturn(category2);

        configurer.setLCategories(builder, requestContext);

        verify(builder).lCat(1, category1);
        verify(builder).lCat(2, category2);
    }

    @Test
    public void setLLocations(){
        Location location1 = mock(Location.class);
        Location location2 = mock(Location.class);
        when(location1.getId()).thenReturn(12);
        when(location2.getId()).thenReturn(34);
        when(requestContext.getLocation(1)).thenReturn(location1);
        when(requestContext.getLocation(2)).thenReturn(location2);

        configurer.setLLocations(builder, requestContext);

        verify(builder).lLoc(1, location1);
        verify(builder).lLoc(2, location2);
    }

    private AbstractGooogleAnalyticsConfigurer createConfigurer() {
        return new AbstractGooogleAnalyticsConfigurer() {
            @Override
            public void configure(GoogleAnalyticsReportBuilder reportBuilder, ThirdPartyRequestContext requestContext) {
            }
        };
    }

}
