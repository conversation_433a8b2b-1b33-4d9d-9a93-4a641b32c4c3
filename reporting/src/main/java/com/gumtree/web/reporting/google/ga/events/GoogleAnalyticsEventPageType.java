package com.gumtree.web.reporting.google.ga.events;

import com.gumtree.zeno.core.domain.PageType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class GoogleAnalyticsEventPageType {

    private GoogleAnalyticsEventPageType() { }

    private static final Logger LOGGER = LoggerFactory.getLogger(GoogleAnalyticsEventPageType.class);

    public static String getPageType(PageType pageType) {
        switch (pageType) {
            case PostAd: return "Post Ad Form";
            case PostAdForm: return "Post Ad Form";
            case EditAd: return "Post Ad Form";
            case R2SEmail: return "Reply To Ad Email";
            case VIP: return "VIP";
            case eVIP: return "VIP-Expired";
            case MyAds: return "Manage Ads";
            case LandingPageCategory: return "Landing Page";
            case HomepageLocation: return "Location Homepage";
            case Homepage: return "Homepage";
            case PopularSearches: return "Popular Search";
            case Favourites: return "My Saved Ads";
            case ResultsBrowse: return "Listing";
            case ResultsSearch: return "Search";
            case ResultsSeller: return "Seller Ads";
            case UserActivationFail: return "Activation Failed";
            case UserActivationSuccess: return "Activate User";
            case StatsAd: return "Ad-stats";
            case SessionExpired: return "Seller Page Expired";
            case Login: return "Login";
            case Logout: return "Logout";
            case DeleteAd: return "Deletead";
            case AccountProfile: return "Company Profile";
            case AccountPackageSummary: return "Package Summary";
            case AccountPackageUsageFull: return "Package Usage";
            case AccountPackageUsageLight: return "Package Usage";
            case PasswordChange: return "Change My Password";
            case AccountDeactivate: return "Deactivate Account";
            case AccountUpdate: return "Update Details";
            case PasswordReset: return "Reset Password";
            case PasswordForgotten: return "Forgotten Password";
            case Outbox: return "Replies Outbox";
            case Inbox: return "Replies Inbox";
            case PasswordResetSent: return "Reset Password Email Sent";
            case OrderReview: return "Order Confirmation";
            case OrderPaymentError: return "Payment Error";
            case PostAdLocation: return "Post Ad Location";
            case PostAdPreview: return "Post Ad Preview";
            case PostAdFeatureTopAdExample: return "Post Ad Feature Featured Example";
            case PostAdFeatureSpotlightExample: return "Post Ad Feature Spotlight Example";
            case PostAdFeatureUrgentExample: return "Post Ad Feature Urgent Example";
            case PostAdFeatureWebsiteExample: return "Post Ad Feature Website Example";
            case AccountCreateSuccess: return "Register Confirmation";
            case AccountCreate: return "Create Account";
            case UserRegistrationForm: return "Create Account";
            case UserRegistrationSuccess: return "Register Confirmation";
            case UserActivationResend: return "Resend Activation";
            case OrderSuccess: return "Order Complete";
            case R2SEmailSuccess: return "Reply To Seller";
            case DeleteAdConfirm: return "Deleteadconfirm";
            case OrderPostAdError: return "Post Ad Order Error";
            case EditAdBumpUpPromo: return "Post Ad Bump Up";
            case SavedSearches: return "Saved_Searches";
            case MyAccount: return "My Account";
            case EmailVerification: return "Email Verification";
            default:
                LOGGER.warn("Unknown page type for {}", pageType);
                return "UNKNOWN";
        }
    }
}
