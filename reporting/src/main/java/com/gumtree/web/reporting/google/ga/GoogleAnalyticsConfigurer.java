package com.gumtree.web.reporting.google.ga;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;

/**
 * Configurer for Google Analytics reports.
 */
public interface GoogleAnalyticsConfigurer<T> {

    /**
     * Configurer a Google Analytics report.
     *
     * @param builder  for building the report
     * @param ctx contextual data for third party related logic
     */
    void configure(GoogleAnalyticsReportBuilder builder, ThirdPartyRequestContext<T> ctx);
}
