package com.gumtree.web.reporting.google.ga.events;

import com.gumtree.zeno.core.domain.PageType;

public abstract class GoogleAnalyticsTrackEvent {

    /* ga params */
    private final String category;
    private final String action;
    private String label;
    private Integer value;
    private Boolean noninteraction;

    public GoogleAnalyticsTrackEvent(PageType pageType, GoogleAnalyticsTrackEventAction action) {
        this.category = GoogleAnalyticsEventPageType.getPageType(pageType);
        this.action = action.getName();
    }

    public GoogleAnalyticsTrackEvent setLabel(String label) {
        this.label = label;
        return this;
    }

    public GoogleAnalyticsTrackEvent setValue(Integer value) {
        this.value = value;
        return this;
    }

    public GoogleAnalyticsTrackEvent setNoninteraction(Boolean noninteraction) {
        this.noninteraction = noninteraction;
        return this;
    }

    public String getCategory() {
        return category;
    }

    public String getAction() {
        return action;
    }

    public String getLabel() {
        return label;
    }

    public Integer getValue() {
        return value;
    }

    public Boolean getNoninteraction() {
        return noninteraction;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        GoogleAnalyticsTrackEvent that = (GoogleAnalyticsTrackEvent) o;

        if (action != null ? !action.equals(that.action) : that.action != null) {
            return false;
        }
        if (category != null ? !category.equals(that.category) : that.category != null) {
            return false;
        }
        if (label != null ? !label.equals(that.label) : that.label != null) {
            return false;
        }
        if (noninteraction != null ? !noninteraction.equals(that.noninteraction) : that.noninteraction != null) {
            return false;
        }
        if (value != null ? !value.equals(that.value) : that.value != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = category != null ? category.hashCode() : 0;
        result = 31 * result + (action != null ? action.hashCode() : 0);
        result = 31 * result + (label != null ? label.hashCode() : 0);
        result = 31 * result + (value != null ? value.hashCode() : 0);
        result = 31 * result + (noninteraction != null ? noninteraction.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "GoogleAnalyticsTrackEvent{"
                + "category='" + category + '\''
                + ", action='" + action + '\''
                + ", label='" + label + '\''
                + ", value=" + value
                + ", noninteraction=" + noninteraction
                + '}';
    }
}
