package com.gumtree.user.service.support.builder;

import com.gumtree.user.service.model.AuthenticationResponse;
import com.gumtree.user.service.model.GumtreeAccessToken;
import com.gumtree.user.service.support.factory.GumtreeAccessTokenFactory;

public final class AuthenticationResponseBuilder {

    private Long accountId;
    private GumtreeAccessToken gumtreeAccessToken;
    private String username;

    private AuthenticationResponseBuilder() {

    }

    public AuthenticationResponseBuilder setAccountId(Long accountId) {
        this.accountId = accountId;
        return this;
    }

    public AuthenticationResponseBuilder setGumtreeAccessToken(GumtreeAccessToken gumtreeAccessToken) {
        this.gumtreeAccessToken = gumtreeAccessToken;
        return this;
    }

    public AuthenticationResponseBuilder setGumtreeAccessToken(String token) {
        this.gumtreeAccessToken = GumtreeAccessTokenFactory.createFromString(token);
        return this;
    }

    public AuthenticationResponseBuilder setUsername(String username) {
        this.username = username;
        return this;
    }

    public static AuthenticationResponseBuilder builder() {
        return new AuthenticationResponseBuilder();
    }

    public AuthenticationResponse build() {
        AuthenticationResponse authenticationResponse = new AuthenticationResponse();
        authenticationResponse.setGumtreeAccessToken(gumtreeAccessToken);
        authenticationResponse.setUsername(username);
        authenticationResponse.setAccountId(accountId);
        return authenticationResponse;
    }


}
