package com.gumtree.user.service.support.builder;

import com.gumtree.user.service.model.ChangePasswordRequest;
import com.gumtree.user.service.model.VerificationKeyType;

public final class ChangePasswordRequestBuilder {

    private String password;
    private String confirmedPassword;
    private String username;
    private String verificationKey;
    private VerificationKeyType verificationKeyType;
    private String client;

    private ChangePasswordRequestBuilder() {

    }

    public static ChangePasswordRequestBuilder builder() {
        return new ChangePasswordRequestBuilder();
    }

    public ChangePasswordRequestBuilder setPassword(String password) {
        this.password = password;
        return this;
    }

    public ChangePasswordRequestBuilder setConfirmedPassword(String confirmedPassword) {
        this.confirmedPassword = confirmedPassword;
        return this;
    }

    public ChangePasswordRequestBuilder setUsername(String username) {
        this.username = username;
        return this;
    }

    public ChangePasswordRequestBuilder setVerificationKey(String verificationKey) {
        this.verificationKey = verificationKey;
        return this;
    }

    public ChangePasswordRequestBuilder setVerificationKeyType(VerificationKeyType verificationKeyType) {
        this.verificationKeyType = verificationKeyType;
        return this;
    }

    public ChangePasswordRequestBuilder setClient(String client) {
        this.client = client;
        return this;
    }

    public ChangePasswordRequest build() {
        ChangePasswordRequest changePasswordRequest = new ChangePasswordRequest();
        changePasswordRequest.setClient(client);
        changePasswordRequest.setVerificationKey(verificationKey);
        changePasswordRequest.setPassword(password);
        changePasswordRequest.setUsername(username);
        changePasswordRequest.setConfirmedPassword(confirmedPassword);
        changePasswordRequest.setVerificationKeyType(verificationKeyType);
        return changePasswordRequest;
    }


}
