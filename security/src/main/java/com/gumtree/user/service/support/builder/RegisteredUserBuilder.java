package com.gumtree.user.service.support.builder;

import com.gumtree.user.service.model.RegisteredUser;

public final class RegisteredUserBuilder {

    private Long userId;
    private String userStatus;

    private RegisteredUserBuilder() {

    }

    public RegisteredUserBuilder setUserId(Long userId) {
        this.userId = userId;
        return this;
    }

    public RegisteredUserBuilder setUserStatus(String userStatus) {
        this.userStatus = userStatus;
        return this;
    }

    public static RegisteredUserBuilder builder() {
        return new RegisteredUserBuilder();
    }

    public RegisteredUser build() {
        RegisteredUser registeredUser = new RegisteredUser();
        registeredUser.setUserId(userId);
        registeredUser.setUserStatus(userStatus);
        return registeredUser;
    }

}
