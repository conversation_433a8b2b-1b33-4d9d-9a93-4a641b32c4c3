package com.gumtree.web.security.exception;

import java.util.List;
import java.util.Map;

public class FormValidationException extends RuntimeException {
    private final Map<String, List<String>> formErrors;
    /**
     * Constructor.
     *
     * @param message the error message
     * @param formErrors the form errors
     */
    public FormValidationException(String message, Map<String, List<String>> formErrors) {
        super(message);
        this.formErrors = formErrors;
    }

    public Map<String, List<String>> getFormErrors() {
        return formErrors;
    }
}
