package com.gumtree.web.security.shiro;

import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.common.util.security.exception.MissingUsernameException;
import com.gumtree.user.service.model.AuthenticationProvider;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.util.RequestUtils;
import com.gumtree.web.cookie.CookieCutterNotFoundException;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.MessageCentreCookieHelper;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.exception.UsernameInvalidException;
import com.gumtree.web.security.login.LoginFailure;
import com.gumtree.web.security.login.LoginUtils;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.apache.shiro.web.util.WebUtils;
import org.jboss.resteasy.client.ClientResponseFailure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URI;
import java.util.Optional;

import static com.gumtree.metrics.AuthenticationMetrics.Action;
import static com.gumtree.metrics.AuthenticationMetrics.Status;
import static com.gumtree.metrics.AuthenticationMetrics.incrementCounter;

/**
 * Extension of the {@link FormAuthenticationFilter} that always forces remember me functionality and translates
 * authentication exceptions into consistent message codes.
 */
public class GumtreeFormAuthenticationFilter extends FormAuthenticationFilter {

    private Logger logger = LoggerFactory.getLogger(GumtreeFormAuthenticationFilter.class);

    public static final String LOGIN_FAILURE = "login.failure";
    public static final String LOGIN_PLATFORM = "loginPlatform";
    public static final String IS_OPT_IN_MARKETING = "isOptInMarketing";
    public static final String RECAPTCHA_PARAM_NAME = "g-recaptcha-response";

    private final UrlScheme urlScheme;
    private final BushfireApi bushfireApi;
    private final LoginUtils loginUtils;
    private final UserSession userSession;
    private final MessageCentreCookieHelper messageCentreCookieHelper;
    private final CookieResolver cookieResolver;
    private final MeterRegistry meterRegistry;

    /**
     * Constructor.
     *
     * @param urlScheme     url scheme
     * @param bushfireApi   the api
     * @param loginUtils    login utils.
     */
    public GumtreeFormAuthenticationFilter(
            UrlScheme urlScheme,
            BushfireApi bushfireApi,
            LoginUtils loginUtils,
            UserSession userSession,
            MessageCentreCookieHelper messageCentreCookieHelper,
            CookieResolver cookieResolver,
            MeterRegistry meterRegistry) {
        this.urlScheme = urlScheme;
        this.bushfireApi = bushfireApi;
        this.loginUtils = loginUtils;
        this.userSession = userSession;
        this.messageCentreCookieHelper = messageCentreCookieHelper;
        this.cookieResolver = cookieResolver;
        this.meterRegistry = meterRegistry;
    }

    @Override
    protected final boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        AuthenticationToken token = createToken(request, response);

        String username = (String) token.getPrincipal();
        try {
            if (isLoginRequest(request, response)
                    && isLoginSubmission(request, response)
                    && LoginType.getType(request) == LoginType.NEW_USER) {

                NewUserLoginFlowContext context = new NewUserLoginFlowContext(
                        isAPostAdRequest() ? java.util.Optional.ofNullable(username).orElse("") : username,
                        request,
                        response,
                        loginUtils,
                        urlScheme,
                        userSession);

                getUserType(username).handle(context);
                return false;
            }
        } catch (AuthenticationException ex) {
            return onLoginFailure(token, ex, request, response);
        }

        return super.onAccessDenied(request, response);
    }

    private NewUserLoginFlowType getUserType(String username) {
        User user = null;
        //If the username for the new user is blank, it might mean they are coming from the new post ad registration flow
        if (!StringUtils.hasLength(username)) {
            if (isAPostAdRequest()) {
                return NewUserLoginFlowType.userType(user);
            } else {
                throw new MissingUsernameException();
            }
        }

        try {
            user = bushfireApi.userApi().getUserByEmailAddress(username);
        } catch (ClientResponseFailure ex) {
            if (ex.getResponse().getStatus() != 404) {
                throw new UsernameInvalidException();
            }
        }
        return NewUserLoginFlowType.userType(user);
    }

    @Override
    protected final boolean isRememberMe(ServletRequest request) {
        return true;
    }

    @Override
    public final String getFailureKeyAttribute() {
        return LOGIN_FAILURE;
    }

    @Override
    protected final boolean isLoginRequest(ServletRequest request, ServletResponse response) {
        try {
            URI uri = new URI(getLoginUrl());
            return pathsMatch(uri.getPath(), request);
        } catch (Exception ex) {
            throw new IllegalStateException("Login URL not a valid URI");
        }
    }

    @Override
    protected final void saveRequest(ServletRequest request) {
        loginUtils.saveRequest(request);
    }

    @Override
    protected final void setFailureAttribute(final ServletRequest request, final AuthenticationException ae) {
        request.setAttribute(LOGIN_FAILURE, LoginFailure.failureFor(ae));
    }

    @Override
    protected final void redirectToLogin(ServletRequest request, ServletResponse response) throws IOException {
        loginUtils.redirectToLogin(request, response, getLoginUrl());
    }

    @Override
    protected void issueSuccessRedirect(ServletRequest request, ServletResponse response) throws Exception {
        String url = getSuccessUrl();
        loginUtils.redirectToSavedRequest(request, response, url);
    }

    @Override
    protected final boolean onLoginSuccess(
            AuthenticationToken token,
            Subject subject,
            ServletRequest request,
            ServletResponse response) throws Exception {

        loginUtils.clearNewUserEmailAddressFromSession();
        loginUtils.setNewUserMustLoginToStartPostAdFlow(true);
        loginUtils.setUserDirty(true);
        messageCentreCookieHelper.addMessageCentreCookieForPrincipal(request, response);

        if (token instanceof GumtreeAuthenticationToken) {
            GumtreeAuthenticationToken gtToken = (GumtreeAuthenticationToken) token;
            updateAuthenticationProviderMetrics(gtToken.getAuthenticationProvider());
        }

        return super.onLoginSuccess(token, subject, request, response);
    }

    private void updateAuthenticationProviderMetrics(AuthenticationProvider provider){
        switch (provider) {
            case GOOGLE:
            case GOOGLEID:
                incrementCounter(meterRegistry, Action.GOOGLE_LOGIN, Status.SUCCESS);
                break;
            case FACEBOOK:
                incrementCounter(meterRegistry, Action.FACEBOOK_LOGIN, Status.SUCCESS);
                break;
            case GUMTREE:
                incrementCounter(meterRegistry, Action.GUMTREE_LOGIN, Status.SUCCESS);
                break;
            default:
                logger.warn("Authentication attempt with unknown provider  {}", provider.toString());
                incrementCounter(meterRegistry, Action.UNKNOWN_PROVIDER_LOGIN, Status.SUCCESS);
        }
    }

    @Override
    protected AuthenticationToken createToken(ServletRequest request, ServletResponse response) {
        AuthenticationProvider authProvider = determineAuthenticationProvider(request);
        String username = getUsername(request);
        String password = getPassword(request);
        String host = getHost(request);
        Boolean isOptInMarketing = BooleanUtils.toBooleanObject(request.getParameter(IS_OPT_IN_MARKETING));
        String recaptcha = request.getParameter(RECAPTCHA_PARAM_NAME);
        Optional<String> ipAddress = RequestUtils.resolveRemoteIp((HttpServletRequest) request);
        Optional<String> threatmetrixSessionId = getThreatMetrixSessionId(request);

        return GumtreeAuthenticationToken.builder()
                .withUsername(username)
                .withPassword(password)
                .withAuthenticationProvider(authProvider)
                .withHost(host)
                .withRecaptchaResponse(recaptcha)
                .withIpAddress(ipAddress)
                .withOptInMarketing(isOptInMarketing)
                .withthreatmetrixSessionId(threatmetrixSessionId)
                .build();
    }

    private AuthenticationProvider determineAuthenticationProvider(ServletRequest request) {
        String loginPlatform = request.getParameter(LOGIN_PLATFORM);
        if (loginPlatform != null) {
            switch (loginPlatform) {
                case "facebook": return AuthenticationProvider.FACEBOOK;
                case "googleplus": return AuthenticationProvider.GOOGLEID;
                default: break;
            }
        }

        return AuthenticationProvider.GUMTREE;

    }

    private boolean isAPostAdRequest() {
        return org.apache.commons.lang3.StringUtils.contains(getSavedRequestURI(), "postad");
    }

    private String getSavedRequestURI() {
        return java.util.Optional.ofNullable(WebUtils.getSavedRequest(null))
                .map(savedRequest -> savedRequest.getRequestURI())
                .orElse("");
    }

    private Optional<String> getThreatMetrixSessionId(ServletRequest request) {
        try {
            ThreatMetrixCookie tmCookie = cookieResolver.resolve((HttpServletRequest) request, ThreatMetrixCookie.class);
            return Optional.of(tmCookie.getDefaultValue());
        } catch (CookieCutterNotFoundException e) {
            logger.info("ThreatMetrix Cookie missing");
            return Optional.empty();
        }
    }
}

