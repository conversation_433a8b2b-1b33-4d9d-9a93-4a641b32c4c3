package com.gumtree.web.security.shiro;

import com.gumtree.web.security.SecurityHelper;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.security.shiro.filter.BaseCustomLoginFilter;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import static com.gumtree.web.security.config.CommonSecurityConfig.BUSHFIRE_SOFT_LOGIN_COOKIE_NAME;

/**
 * Filter for new user login.
 */
public final class NewUserLoginFilter extends BaseCustomLoginFilter {

    private final LoginUtils loginUtils;
    private SecurityHelper securityHelper;
    private static final Logger log = LoggerFactory.getLogger(NewUserLoginFilter.class);
    public static final String PHONE_VERIFY_PLATFORM = "phone_verify_platform";
    /**
     * Constructor.
     *
     * @param loginUtils login utils helper
     */
    public NewUserLoginFilter(LoginUtils loginUtils, SecurityHelper securityHelper) {
        this.loginUtils = loginUtils;
        this.securityHelper = securityHelper;
    }

    @Override
    protected boolean internalIsAccessAllowed(ServletRequest request, ServletResponse response) {
            parseAppHeader(request,response);

            Subject subject = getSubject(request, response);

            return loginUtils.getNewUserEmailAddressFromSession() != null
                    || ((subject.isRemembered() || subject.isAuthenticated()) && securityHelper.verifyAccessTokenAndLogoutIfInvalid(subject));

    }

    private void parseAppHeader(ServletRequest request, ServletResponse response) {
        try{
            if (request instanceof HttpServletRequest) {
                HttpServletRequest httpRequest = (HttpServletRequest) request;
                String platform_tag = httpRequest.getHeader(PHONE_VERIFY_PLATFORM);

                if (!"app".equals(platform_tag) || hasCookie(httpRequest,BUSHFIRE_SOFT_LOGIN_COOKIE_NAME)) {
                    return;
                }

                String gt_c_auth = httpRequest.getHeader("X-ECG-Authorization-User");
                String userName = httpRequest.getHeader("Authorisation-User-Email");

                GumtreeAppToken appToken = new GumtreeAppToken(userName, gt_c_auth);
                Subject subject = SecurityUtils.getSubject();
                subject.login(appToken);
            }
        }catch (Exception e){
            log.error("An error occurred while converting ServletRequest to HttpServletRequest during the login verification.",e);
        }
    }

    private boolean hasCookie(HttpServletRequest request, String cookieName) {
        Cookie[] cookies = request.getCookies();

        if (cookies == null) {
            return false;
        }

        for (Cookie cookie : cookies) {
            if (cookieName.equals(cookie.getName())) {
                return true;
            }
        }

        return false;
    }
}
