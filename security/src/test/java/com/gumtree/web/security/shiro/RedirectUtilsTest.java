package com.gumtree.web.security.shiro;

import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.netflix.config.ConfigurationManager;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.util.SavedRequest;
import org.apache.shiro.web.util.WebUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.StringReader;
import java.util.Optional;
import java.util.Properties;

import static com.gumtree.web.security.shiro.RedirectUtils.*;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

/**
 */
public class RedirectUtilsTest extends BaseShiroTest {

    private static final String FALLBACK_URL = "/manage/ads";
    private Session session;

    private HttpServletRequest servletRequest;
    private HttpServletResponse servletResponse;

    private Subject subject;

    private CookieResolver cookieResolver;

    private PermanentCookie permanentCookie;

    private static String buyerBaseUri = "http://www.gumtree.com";

    @Before
    public void init() {
        loadProperties();
        session = mock(Session.class);
        servletRequest = mock(HttpServletRequest.class);
        servletResponse = mock(HttpServletResponse.class);
        subject = mock(Subject.class);
        cookieResolver = mock(CookieResolver.class);

        when(subject.getSession()).thenReturn(session);

        setSubject(subject);

        permanentCookie = new PermanentCookie("",3, "/");

        when(cookieResolver.resolve(servletRequest, PermanentCookie.class)).thenReturn(permanentCookie);
    }

    private void loadProperties() {
        Properties properties = new Properties();
        properties.setProperty("gumtree.url.buyer.base_uri", "");
        properties.setProperty("gumtree.url.seller.base_uri", "");
        properties.setProperty("gumtree.url.seller.secure.base_uri", "");
        properties.setProperty("gumtree.url.reply.base_uri", "");
        ConfigurationManager.loadProperties(properties);
    }

    @Test
    public void shouldAllowOnlyGumtreeReferer() {
        assertTrue(RedirectUtils.isValidCallBackUrl("http://www.gumtree.com/p/cars/123"));
        assertTrue(RedirectUtils.isValidCallBackUrl("https://www.gumtree.com/p/cars/123"));
        assertTrue(RedirectUtils.isValidCallBackUrl("http://my.gumtree.com/postad"));
        assertTrue(RedirectUtils.isValidCallBackUrl("https://my.gumtree.com/postad"));
        assertTrue(RedirectUtils.isValidCallBackUrl("https://www.connect.gumtree.io/p/cars/123"));
        assertTrue(RedirectUtils.isValidCallBackUrl("http://www.connect.gumtree.io/p/cars/123"));
        assertTrue(RedirectUtils.isValidCallBackUrl("http://dev.gumtree.com/p/cars/123"));
        assertTrue(RedirectUtils.isValidCallBackUrl("http://dev.gumtree.com:8080/p/cars/123"));
        assertTrue(RedirectUtils.isValidCallBackUrl("https://dev.gumtree.com/"));
        assertTrue(RedirectUtils.isValidCallBackUrl("http://dev.gumtree.com/"));

        assertFalse(RedirectUtils.isValidCallBackUrl("http://dev.gumtree.com"));
        assertFalse(RedirectUtils.isValidCallBackUrl("http://gumtree.com"));
        assertFalse(RedirectUtils.isValidCallBackUrl("http://www.sme.sk/p/cars/123"));
        assertFalse(RedirectUtils.isValidCallBackUrl("iphone browser"));
        assertFalse(RedirectUtils.isValidCallBackUrl(" "));
        assertFalse(RedirectUtils.isValidCallBackUrl(""));
        assertFalse(RedirectUtils.isValidCallBackUrl(null));
        assertFalse(RedirectUtils.isValidCallBackUrl("https://xssed.de//www.gumtree.com/p/cars/123"));
        assertFalse(RedirectUtils.isValidCallBackUrl("https://2F%2Fxssed.de%2f%2fwww.gumtree.com/p/cars/123"));
        assertFalse(RedirectUtils.isValidCallBackUrl("https://%Dwww.gumtree.com/p/cars/123"));
        assertFalse(RedirectUtils.isValidCallBackUrl("https://evil.com#.www.connect.gumtree.io/my-account/saved-searches/add"));
    }

    @Test
    public void testSaveRequestForRequestWithNoQueryString() {
        when(servletRequest.getRequestURL()).thenReturn(new StringBuffer("http://www.test.com/my/path"));
        when(servletRequest.getRequestURI()).thenReturn("/my/path");
        when(servletRequest.getQueryString()).thenReturn(null);
        when(servletRequest.getMethod()).thenReturn("GET");

        RedirectUtils.saveRequest(servletRequest);

        ArgumentCaptor<SavedRequest> savedRequestCaptor = ArgumentCaptor.forClass(SavedRequest.class);

        verify(session).setAttribute(eq(WebUtils.SAVED_REQUEST_KEY), savedRequestCaptor.capture());

        SavedRequest savedRequest = savedRequestCaptor.getValue();

        assertThat(savedRequest.getMethod(), equalTo("GET"));
        assertThat(savedRequest.getQueryString(), equalTo(null));
        assertThat(savedRequest.getRequestURI(), equalTo("/my/path"));
        assertThat(savedRequest.getRequestUrl(), equalTo("http://www.test.com/my/path"));
    }

    @Test
    public void testSaveRequestForRequestWithAQueryString() {
        when(servletRequest.getRequestURL()).thenReturn(new StringBuffer("http://www.test.com/my/path"));
        when(servletRequest.getQueryString()).thenReturn("a=1&b=2&c=3");
        when(servletRequest.getRequestURI()).thenReturn("/my/path");
        when(servletRequest.getMethod()).thenReturn("GET");

        RedirectUtils.saveRequest(servletRequest);

        ArgumentCaptor<SavedRequest> savedRequestCaptor = ArgumentCaptor.forClass(SavedRequest.class);

        verify(session).setAttribute(eq(WebUtils.SAVED_REQUEST_KEY), savedRequestCaptor.capture());

        SavedRequest savedRequest = savedRequestCaptor.getValue();

        assertThat(savedRequest.getMethod(), equalTo("GET"));
        assertThat(savedRequest.getQueryString(), equalTo("a=1&b=2&c=3"));
        assertThat(savedRequest.getRequestURI(), equalTo("/my/path"));
        assertThat(savedRequest.getRequestUrl(), equalTo("http://www.test.com/my/path?a=1&b=2&c=3"));
    }

    @Test
    public void shouldBeAbleToStoreThePayloadOfAPostRequest() throws Exception {
        //        Given
        String expectedPayload = "payload";
        when(servletRequest.getRequestURL()).thenReturn(new StringBuffer("http://www.test.com/my/path"));
        when(servletRequest.getQueryString()).thenReturn("a=1&b=2&c=3");
        when(servletRequest.getRequestURI()).thenReturn("/my/path");
        when(servletRequest.getMethod()).thenReturn("POST");
        when(servletRequest.getReader()).thenReturn(new BufferedReader(new StringReader(expectedPayload)));

        //        When
        RedirectUtils.saveHttpPostPayload(servletRequest);

        //        Then
        verify(session).setAttribute(eq(RedirectUtils.HTTP_POST_PAYLOAD), eq(expectedPayload));
    }

    @Test
    public void shouldBeAbleToRetrievePayloadFromSession() {
        //        Given
        String payload = "payload";
        when(session.getAttribute(RedirectUtils.HTTP_POST_PAYLOAD)).thenReturn(payload);

        //        When
        Optional<String> redirectedPayload = RedirectUtils.getAndClearHttpPostPayload();

        //        Then
        assertThat(redirectedPayload.get(), equalTo("payload"));
    }

    @Test
    public void shouldReturnEmptyOptionalIfNoPayloadInSession() {
        //        Given
        String payload = "payload";
        when(session.getAttribute(RedirectUtils.HTTP_POST_PAYLOAD)).thenReturn(null);

        //        When
        Optional<String> redirectedPayload = RedirectUtils.getAndClearHttpPostPayload();

        //        Then
        assertThat(redirectedPayload.isPresent(), equalTo(false));
    }

    @Test
    public void shouldNotStoreThePayloadOfIfGetRequest() throws Exception {
        //        Given
        when(servletRequest.getRequestURL()).thenReturn(new StringBuffer("http://www.test.com/my/path"));
        when(servletRequest.getQueryString()).thenReturn("a=1&b=2&c=3");
        when(servletRequest.getRequestURI()).thenReturn("/my/path");
        when(servletRequest.getMethod()).thenReturn("GET");
        when(servletRequest.getReader()).thenReturn(new BufferedReader(new StringReader("payload")));

        //        When
        RedirectUtils.saveHttpPostPayload(servletRequest);

        //        Then
        verifyZeroInteractions(session);
    }

    // Keeping single test for sanity. We are not sending GA events.
    @Test
    public void shouldSendGAEventWithManageAccountMappedCorrectlyBeforeRedirecting() throws Exception {
        String att = "/manage-account";
        when(session.getAttribute("loginCallbackURL")).thenReturn(att);

        RedirectUtils.redirectToSavedRequest(servletRequest, servletResponse, FALLBACK_URL, cookieResolver);

//        verify(googleAnalyticsService).sendAsyncEvent(eq(getGaEvent(PageType.MyAccount.name(), permanentCookie.getId())));
    }

    @Test
    public void shouldAppendQueryParamsInCallbackUrl() {
        when(servletRequest.getParameter(CALLBACK_PARAM_NAME)).thenReturn("http://dev.gumtree.com/");
        when(servletRequest.getParameter(CALLBACK_QUERY_PARAMS_KEY))
                .thenReturn("cb%3Dhttp%3A%2F%2Fdev.gumtree.com%2Fvip%26rt%3dtoken");
        Optional<String> result = RedirectUtils.getCallbackURL(servletRequest);
        assertThat(result.get(), equalTo("http://dev.gumtree.com/?qp=cb%3Dhttp%3A%2F%2Fdev.gumtree.com%2Fvip%26rt%3dtoken"));
    }

    @Test
    public void shouldReturnEmptyIfCallbackIsNotValid() {
        when(servletRequest.getParameter(CALLBACK_PARAM_NAME)).thenReturn("loginURL");
        when(servletRequest.getParameter(CALLBACK_QUERY_PARAMS_KEY))
                .thenReturn("cb%3Dhttp%3A%2F%2Fdev.gumtree.com%2Fvip%26rt%3dtoken");
        Optional<String> result = RedirectUtils.getCallbackURL(servletRequest);
        assertThat(result.isPresent(), equalTo(false));
    }
    @Test
    public void validateCallBackSuccessful() {
        // given
        String callback = buyerBaseUri + "/p/toyota/vip-page/1262984769";

        // when - then
        try {
            validateCallbackUrl(callback);
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test(expected = InvalidRedirectUrlException.class)
    public void shouldReturnAnInvalidRedirectUrlExceptionIfHostIsNotValid() throws Exception {
        // given
        String callback = "www.google.com/p/toyota/vip-page/1262984769";
        // when
        validateCallbackUrl(callback);
    }

    @Test(expected = InvalidRedirectUrlException.class)
    public void shouldReturnInvalidRedirectIfHostHas() throws Exception {
        // given
        String callback = "https%3A%2F%2Fxssed.de%2f%2f.www.gumtree.com%2Fp%2Flaptops%2F13.3-dell-latitude-e4300-intel-" +
                "p9600-2.5ghz-4gb-ram-320gb-hdd-sim-slot-dock-no-offers-please-%2F1372998084";
        // when
        validateCallbackUrl(callback);
    }

    @Test(expected = RuntimeException.class)
    public void shouldReturnAnURISyntaxExceptionIfCallbackCannotBeParsed() throws Exception {
        // given
        String callback = "www.google.com/p/toyota/vip-pa  ge/1262984769";
        // when
        validateCallbackUrl(callback);
    }
}
