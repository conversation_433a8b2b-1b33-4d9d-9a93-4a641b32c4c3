package com.gumtree.web.security.shiro;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.common.properties.GtPropManager;
import com.gumtree.common.util.security.exception.MissingPasswordException;
import com.gumtree.common.util.security.exception.MissingUsernameAndPasswordException;
import com.gumtree.common.util.security.exception.MissingUsernameException;
import com.gumtree.common.util.security.exception.UnknownUserAccountException;
import com.gumtree.common.util.security.exception.UserAccountNotActiveException;
import com.gumtree.common.util.security.exception.UserAccountSuspendedException;
import com.gumtree.config.SellerProperty;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.MessageCentreCookieHelper;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.login.LoginUtils;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InOrder;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.gumtree.web.security.login.LoginFailure.GENERIC_AUTHENTICATION_ERROR;
import static com.gumtree.web.security.login.LoginFailure.INCORRECT_CREDENTIALS;
import static com.gumtree.web.security.login.LoginFailure.MISSING_PASSWORD;
import static com.gumtree.web.security.login.LoginFailure.MISSING_USERNAME;
import static com.gumtree.web.security.login.LoginFailure.MISSING_USERNAME_AND_PASSWORD;
import static com.gumtree.web.security.login.LoginFailure.UNKNOWN_ACCOUNT;
import static com.gumtree.web.security.login.LoginFailure.USER_ACCOUNT_NOT_ACTIVE;
import static com.gumtree.web.security.login.LoginFailure.USER_ACCOUNT_SUSPENDED;
import static com.gumtree.web.security.shiro.GumtreeFormAuthenticationFilter.LOGIN_FAILURE;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

public class GumtreeFormAuthenticationFilterTest extends BaseShiroTest {

    private HttpServletRequest servletRequest;

    private HttpServletResponse servletResponse;

    private GumtreeFormAuthenticationFilter alwaysRememberMeFilter;

    private UrlScheme urlScheme;

    private BushfireApi bushfireApi;

    private UserApi userApi;

    private LoginUtils loginUtils;

    private MessageCentreCookieHelper messageCentreCookieHelper;

    private Subject subject;

    private UserSession userSession;

    private CookieResolver cookieResolver;

    private ThreatMetrixCookie tmCookie;

    private MeterRegistry meterRegistry;

    @Before
    public void init() {
        subject = mock(Subject.class);
        servletRequest = mock(HttpServletRequest.class);
        servletResponse = mock(HttpServletResponse.class);
        loginUtils = mock(LoginUtils.class);
        urlScheme = mock(UrlScheme.class);
        bushfireApi = mock(BushfireApi.class);
        userApi = mock(UserApi.class);
        messageCentreCookieHelper = mock(MessageCentreCookieHelper.class);
        when(bushfireApi.userApi()).thenReturn(userApi);
        userSession = mock(UserSession.class);
        cookieResolver = mock(CookieResolver.class);
        tmCookie = mock(ThreatMetrixCookie.class);
        meterRegistry = mock(MeterRegistry.class);

        alwaysRememberMeFilter =
                new GumtreeFormAuthenticationFilter(urlScheme, bushfireApi, loginUtils, userSession,
                        messageCentreCookieHelper, cookieResolver, meterRegistry) {
                    @Override
                    protected boolean executeLogin(ServletRequest request, ServletResponse response) throws Exception {
                        // do nothing in test
                        return true;
                    }

                    @Override
                    protected void issueSuccessRedirect(ServletRequest request, ServletResponse response) throws Exception {
                        // do nothing in test
                    }
        };
        when(urlScheme.postAdUrl()).thenReturn("http://www.gumtree.com/legacy/postad/url");
        setSubject(subject);
    }

    @Test
    public void rememberMeIsAlwaysTrue() {
        assertThat(alwaysRememberMeFilter.isRememberMe(servletRequest), equalTo(true));
    }

    @Test
    public void failureAttributeNameIsCorrect() {
        assertThat(alwaysRememberMeFilter.getFailureKeyAttribute(), equalTo(LOGIN_FAILURE));
    }

    @Test
    public void unknownAccountExceptionTranslatedIntoCorrectAuthenticationFailure() {
        alwaysRememberMeFilter.setFailureAttribute(servletRequest, new UnknownUserAccountException());
        verify(servletRequest).setAttribute(LOGIN_FAILURE, UNKNOWN_ACCOUNT);
    }

    @Test
    public void incorrectCredentialsExceptionTranslatedIntoCorrectAuthenticationFailure() {
        alwaysRememberMeFilter.setFailureAttribute(servletRequest, new IncorrectCredentialsException());
        verify(servletRequest).setAttribute(LOGIN_FAILURE, INCORRECT_CREDENTIALS);
    }

    @Test
    public void userAccountSuspendedExceptionTranslatedIntoCorrectAuthenticationFailure() {
        alwaysRememberMeFilter.setFailureAttribute(servletRequest, new UserAccountSuspendedException());
        verify(servletRequest).setAttribute(LOGIN_FAILURE, USER_ACCOUNT_SUSPENDED);
    }

    @Test
    public void userAccountNotActiveExceptionTranslatedIntoCorrectAuthenticationFailure() {
        alwaysRememberMeFilter.setFailureAttribute(servletRequest, new UserAccountNotActiveException());
        verify(servletRequest).setAttribute(LOGIN_FAILURE, USER_ACCOUNT_NOT_ACTIVE);
    }

    @Test
    public void missingUsernameExceptionTranslatedIntoCorrectAuthenticationFailure() {
        alwaysRememberMeFilter.setFailureAttribute(servletRequest, new MissingUsernameException());
        verify(servletRequest).setAttribute(LOGIN_FAILURE, MISSING_USERNAME);
    }

    @Test
    public void missingPasswordExceptionTranslatedIntoCorrectAuthenticationFailure() {
        alwaysRememberMeFilter.setFailureAttribute(servletRequest, new MissingPasswordException());
        verify(servletRequest).setAttribute(LOGIN_FAILURE, MISSING_PASSWORD);
    }

    @Test
    public void missingUsernameAndPasswordExceptionTranslatedIntoCorrectAuthenticationFailure() {
        alwaysRememberMeFilter.setFailureAttribute(servletRequest, new MissingUsernameAndPasswordException());
        verify(servletRequest).setAttribute(LOGIN_FAILURE, MISSING_USERNAME_AND_PASSWORD);
    }

    @Test
    public void anyOtherTypeOfAuthenticationExceptionTranslatedIntoCorrectAuthenticationFailure() {
        alwaysRememberMeFilter.setFailureAttribute(servletRequest, new AuthenticationException());
        verify(servletRequest).setAttribute(LOGIN_FAILURE, GENERIC_AUTHENTICATION_ERROR);
    }

    @Test
    public void clearsOtherLoginStatesAndFiresLoginEventOnSuccessfulLogin() throws Exception {
        when(subject.getPrincipal()).thenReturn("<EMAIL>");
        alwaysRememberMeFilter.onLoginSuccess(null, subject, servletRequest, servletResponse);

        InOrder order = inOrder(loginUtils);
        order.verify(loginUtils).clearNewUserEmailAddressFromSession();
        order.verify(loginUtils).setNewUserMustLoginToStartPostAdFlow(true);
        order.verify(loginUtils).setUserDirty(true);
        verify(messageCentreCookieHelper).addMessageCentreCookieForPrincipal(any(HttpServletRequest.class),
                                                                            any(HttpServletResponse.class));
    }

    @Test
    public void whenSubmissionToLoginUrlForExistingUserThenAttemptsToLoginNormally() throws Exception {
        when(cookieResolver.resolve(servletRequest, ThreatMetrixCookie.class)).thenReturn(tmCookie);
        when(tmCookie.getDefaultValue()).thenReturn("i'm the threatmetrix session id");
        setupLoginSubmissionRequest(false);
        alwaysRememberMeFilter.onAccessDenied(servletRequest, servletResponse);
        verifyZeroInteractions(bushfireApi);
        verifyZeroInteractions(userApi);
        verifyZeroInteractions(servletResponse);
        verifyZeroInteractions(userSession);
        verifyZeroInteractions(loginUtils);
    }

    private void setupLoginSubmissionRequest(boolean newUser) {
        alwaysRememberMeFilter.setLoginUrl("http://gumtree.com/login/url");
        when(servletRequest.getContextPath()).thenReturn("");
        when(servletRequest.getRequestURI()).thenReturn("/login/url");
        when(servletRequest.getParameter("newUser")).thenReturn(Boolean.toString(newUser));
        when(servletRequest.getMethod()).thenReturn(AccessControlFilter.POST_METHOD);
        when(servletRequest.getParameter("username")).thenReturn("<EMAIL>");
    }
}
