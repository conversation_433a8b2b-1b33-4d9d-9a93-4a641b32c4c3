package com.gumtree.web.security;

import com.google.common.base.Optional;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.subject.support.SubjectThreadState;
import org.apache.shiro.util.ThreadState;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GtSecurityUtilsTest {
    private static final String USER_EMAIL = "<EMAIL>";

    @Mock private Subject subject;

    @Before
    public void before() {
        ThreadState subjectThreadState = new SubjectThreadState(subject);
        subjectThreadState.bind();
        when(subject.getPrincipal()).thenReturn(USER_EMAIL);
    }

    @Test
    public void shouldReturnNullEmailAddressIfUserIsNotLoggedIn() {
        // given
        when(subject.isRemembered()).thenReturn(false);

        // when
        Optional<String> userEmail = GtSecurityUtils.getRememberedUserEmail();

        // then
        assertThat(userEmail).isEqualTo(Optional.<String>absent());
    }

    @Test
    public void shouldReturnUserEmailAddressIfUserIsLoggedIn() {
        // given
        when(subject.isRemembered()).thenReturn(true);

        // when
        Optional<String> userEmail = GtSecurityUtils.getRememberedUserEmail();

        // then
        assertThat(userEmail).isEqualTo(Optional.of(USER_EMAIL));
    }
}
