package com.gumtree.web.security.shiro;

import com.google.common.base.Optional;
import com.google.common.collect.Sets;
import com.gumtree.api.Ad;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Set;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CategoryFilterServiceTest {
    private static final Long ADVERT_ID = 1177014718L;
    private static final String ADVERT_REPLY_URL = "/reply/" + ADVERT_ID;

    private static final Category ROOT_CATEGORY = new Category(CategoryConstants.ALL_ID, "root", "Root");

    private CategoryFilterService categoryFilterService;

    @Mock
    private CategoryFilterService.AdCategoryIdProvider adCategoryProvider;

    @Mock
    private CategoryModel categoryModel;

    private static final Set<Long> WHITELIST_LOGIN_CATEGORY_LIST = Sets.newHashSet(50L, 60L, 70L, 30L);

    @Before
    public void setup() {
        categoryFilterService = createCategoryFilterService(true);
    }

    // TODO GTALL-864: revisit this (originally built for GTALL-5111)
    @Test
    public void itReturnsFalseIfTheAdCategoryIsWhitelisted() throws Exception {
        //When
        boolean actualResult = categoryFilterService.isLoginRequiredToContact(50L);
        //Then
        assertFalse(actualResult);
    }

    // TODO GTALL-864: revisit this (originally built for GTALL-5111)
    @Test
    public void itReturnsFalseIfDisabled() throws Exception {
        //Given
        categoryFilterService = createCategoryFilterService(false);

        //When
        boolean actualResult = categoryFilterService.isLoginRequiredToContact(50L);
        //Then
        assertTrue(actualResult);
    }

    @Test
    public void itReturnsTrueIfCategoryNotEnabled() throws Exception {
        when(categoryModel.getParentOf(90L)).thenReturn(Optional.of(ROOT_CATEGORY));

        //When
        boolean actualResult = categoryFilterService.isLoginRequiredToContact(90L);

        //Then
        assertTrue(actualResult);
    }

    @Test
    public void itReturnsFalseIfTheAdCategoryIsInTheCategoryList() throws Exception {
        //Given
        when(adCategoryProvider.getAdvertCategoryId(ADVERT_ID)).thenReturn(java.util.Optional.of(50L));
        //When
        boolean actualResult = categoryFilterService.isLoginRequiredToContact(ADVERT_REPLY_URL);

        //Then
        assertFalse(actualResult);
    }

    @Test
    public void itReturnsTrueIfTheAdCategoryIsUnknown() throws Exception {
        //Given
        when(adCategoryProvider.getAdvertCategoryId(ADVERT_ID)).thenReturn(java.util.Optional.empty());
        //When
        boolean actualResult = categoryFilterService.isLoginRequiredToContact(ADVERT_REPLY_URL);

        //Then
        assertTrue(actualResult);
    }

    @Test
    public void itReturnsFalseIfAdCategoryParentIsWhitelisted() throws Exception {
        //Given
        when(adCategoryProvider.getAdvertCategoryId(ADVERT_ID)).thenReturn(java.util.Optional.of(120L));
        when(categoryModel.getParentOf(120L)).thenReturn(Optional.of(new Category(60L, "parent", "Parent")));

        //When
        boolean actualResult = categoryFilterService.isLoginRequiredToContact(ADVERT_REPLY_URL);

        //Then
        assertFalse(actualResult);
    }

    private Ad anAdvert(long categoryId) {
        Ad ad = new Ad();
        ad.setCategoryId(categoryId);

        return ad;
    }

    @Test
    public void itReturnsTrueIfTheCategoryIsNotInTheListOrInTheCategoryTree() throws Exception {
        //Given
        when(adCategoryProvider.getAdvertCategoryId(ADVERT_ID)).thenReturn(java.util.Optional.of(250L));
        when(categoryModel.getParentOf(250L)).thenReturn(Optional.of(new Category(90L, "parent", "Parent")));
        when(categoryModel.getParentOf(90L)).thenReturn(Optional.of(ROOT_CATEGORY));
        //When
        boolean actualResult = categoryFilterService.isLoginRequiredToContact(ADVERT_REPLY_URL);
        //Then
        assertTrue(actualResult);
    }

    @Test
    public void shouldRequireLoginWhenNoWhitelistedCategoriesSpecified() throws Exception {
        categoryFilterService = createCategoryFilterService(Collections.emptySet(), true);

        when(adCategoryProvider.getAdvertCategoryId(ADVERT_ID)).thenReturn(java.util.Optional.of(250L));
        when(categoryModel.getParentOf(250L)).thenReturn(Optional.of(ROOT_CATEGORY));

        // when
        boolean actualResult = categoryFilterService.isLoginRequiredToContact(ADVERT_REPLY_URL);

        // then
        assertTrue(actualResult);
    }

    @Test
    public void itReturnsTrueWhenAdvertCategoryDoesNotHaveParent() throws Exception {
        when(adCategoryProvider.getAdvertCategoryId(ADVERT_ID)).thenReturn(java.util.Optional.of(250L));
        when(categoryModel.getParentOf(250L)).thenReturn(Optional.absent());

        // when
        boolean actualResult = categoryFilterService.isLoginRequiredToContact(ADVERT_REPLY_URL);

        // then
        assertTrue(actualResult);
    }

    private CategoryFilterService createCategoryFilterService(Set<Long> contactForceLoginWhitelistedCategories,
                                                              boolean isLoginRequiredForContactEnabled) {
        return new CategoryFilterService(adCategoryProvider, categoryModel, contactForceLoginWhitelistedCategories, isLoginRequiredForContactEnabled);
    }

    private CategoryFilterService createCategoryFilterService(boolean isLoginRequiredForContactEnabled) {
        return createCategoryFilterService(WHITELIST_LOGIN_CATEGORY_LIST, isLoginRequiredForContactEnabled);
    }
}