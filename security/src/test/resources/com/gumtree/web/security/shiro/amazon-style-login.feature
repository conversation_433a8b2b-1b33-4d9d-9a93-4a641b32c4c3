Feature: Handle user who says they are new from amazon style login page

  Scenario Outline: User enters an existing email address for an active user

    Given a user who has visited the login page <via>
    When the user enters an existing email address for an active user
    And the user says they are new
    And the user clicks continue
    Then the user should be shown an existing user error

  Examples:
    | via                 |
    | directly            |
    | via non-post ad url |

  Scenario Outline: User enters no email address

    Given a user who has visited the login page <via>
    When the user enters no email address
    And the user says they are new
    And the user clicks continue
    Then the user should be shown a missing username error

  Examples:
    | via                 |
    | directly            |
    | via non-post ad url |

  Scenario Outline: User enters an invalid email address

    Given a user who has visited the login page <via>
    When the user enters an invalid email address
    And the user says they are new
    And the user clicks continue
    Then the user should be shown an invalid username error

  Examples:
    | via                 |
    | directly            |
    | via non-post ad url |

  Scenario Outline: New user enters valid email address on login page (directly, or via redirect from non-post ad url)

    Given a user who has visited the login page <requested>
    When the user enters <email_address_type>
    And the user says they are new
    And the user clicks continue
    Then authentication cookies should be cleared
    And the user should have to login to post an ad
    And the user dirty state should be true
    And the user's email address should be stored in the session
    And the user should be redirected to the create account page url

  Examples:
    | requested           | email_address_type                                |
    | directly            | an existing email address for an unactivated user |
    | directly            | an existing email address for a deactivated user  |
    | directly            | a valid non-existing email address                |
    | via non-post ad url | an existing email address for an unactivated user |
    | via non-post ad url | an existing email address for a deactivated user  |
    | via non-post ad url | a valid non-existing email address                |

  Scenario Outline: New user enters valid email address on login page when redirected trying to post an ad

    Given a user who has been redirected to the login page from a <platform> post ad url
    When the user enters <email_address_type>
    And the user says they are new
    And the user clicks continue
    Then authentication cookies should be cleared
    And the user should not have to login to post an ad
    And the user dirty state should be true
    And the user's email address should be stored in the session
    And the user should be redirected to the <redirect_page>

  Examples:
    | platform | email_address_type                                | redirect_page  |
    | legacy   | a valid non-existing email address                | post ad flow   |
    | legacy   | an existing email address for an unactivated user | post ad flow   |
    | legacy   | an existing email address for a deactivated user  | post ad flow   |
    | bushfire | a valid non-existing email address                | post ad flow   |
    | bushfire | an existing email address for an unactivated user | post ad flow   |
    | bushfire | an existing email address for a deactivated user  | post ad flow   |

  Scenario Outline: New user directed to login page via legacy URL should be redirected directly to Bushfire URL

    Given a user who has been redirected to the login page after visiting <url>
    When the user enters <email_address_type>
    And the user says they are new
    And the user clicks continue
    Then authentication cookies should be cleared
    And the user should not have to login to post an ad
    And the user dirty state should be true
    And the user's email address should be stored in the session
    And the user should be redirected directly to <redirect_url>

  Examples:
    | url                                                 | email_address_type                                | redirect_url              |
    | /add_posting.html                                   | a valid non-existing email address                | /postad                   |
    | /add_posting.html                                   | an existing email address for an unactivated user | /postad                   |
    | /add_posting.html                                   | an existing email address for a deactivated user  | /postad                   |
    | /add_posting.html?category_id=1                     | a valid non-existing email address                | /postad?categoryId=1      |
    | /add_posting.html?category_id=1                     | an existing email address for an unactivated user | /postad?categoryId=1      |
    | /add_posting.html?category_id=1                     | an existing email address for a deactivated user  | /postad?categoryId=1      |
    | /add_posting.html?location_id=uk                    | a valid non-existing email address                | /postad                   |
    | /add_posting.html?location_id=uk                    | an existing email address for an unactivated user | /postad                   |
    | /add_posting.html?location_id=uk                    | an existing email address for a deactivated user  | /postad                   |
    | /add_posting.html?location_id=uk&category_id=1000   | a valid non-existing email address                | /postad?categoryId=1000   |
    | /add_posting.html?location_id=uk&category_id=1000   | an existing email address for an unactivated user | /postad?categoryId=1000   |
    | /add_posting.html?location_id=uk&category_id=1000   | an existing email address for a deactivated user  | /postad?categoryId=1000   |
    | /add_posting.html?category_id=100000&location_id=uk | a valid non-existing email address                | /postad?categoryId=100000 |
    | /add_posting.html?category_id=100000&location_id=uk | an existing email address for an unactivated user | /postad?categoryId=100000 |
    | /add_posting.html?category_id=100000&location_id=uk | an existing email address for a deactivated user  | /postad?categoryId=100000 |

  Scenario Outline: New user directed to login page via bushfire URL, should be redirected directly to Bushfire URL

    Given a user who has been redirected to the login page after visiting <url>
    When the user enters <email_address_type>
    And the user says they are new
    And the user clicks continue
    Then authentication cookies should be cleared
    And the user should not have to login to post an ad
    And the user dirty state should be true
    And the user's email address should be stored in the session
    And the user should be redirected directly to <redirect_url>

  Examples:
    | url                                          | email_address_type                                | redirect_url              |
    | /postad                                      | a valid non-existing email address                | /postad                   |
    | /postad                                      | an existing email address for an unactivated user | /postad                   |
    | /postad                                      | an existing email address for a deactivated user  | /postad                   |
    | /postad?categoryId=1000                      | a valid non-existing email address                | /postad?categoryId=1000   |
    | /postad?categoryId=1000                      | an existing email address for an unactivated user | /postad?categoryId=1000   |
    | /postad?categoryId=1000                      | an existing email address for a deactivated user  | /postad?categoryId=1000   |

