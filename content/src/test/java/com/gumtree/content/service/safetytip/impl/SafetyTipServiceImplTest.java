package com.gumtree.content.service.safetytip.impl;

import com.google.common.base.Optional;
import com.gumtree.api.category.domain.Category;
import com.gumtree.content.domain.SafetyTip;
import com.gumtree.content.repository.SafetyTipRepository;
import com.gumtree.content.service.safetytip.SafetyTipService;
import com.gumtree.service.category.CategoryService;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class SafetyTipServiceImplTest {

    private SafetyTipService safetyTipService = new SafetyTipServiceImpl();
    private SafetyTipRepository safetyTipRepository;
    private CategoryService categoryService;

    @Before
    public void init() {
        safetyTipRepository = mock(SafetyTipRepository.class);
        categoryService = mock(CategoryService.class);

        ReflectionTestUtils.setField(safetyTipService, "safetyTipRepository", safetyTipRepository);
        ReflectionTestUtils.setField(safetyTipService, "categoryService", categoryService);
    }

    @Test
    public void getSafetyTipsByCategoryIdReturnsExplicitCategorySafetyTips() {

        SafetyTip safetyTip1 = new SafetyTip();
        SafetyTip safetyTip2 = new SafetyTip();
        List<SafetyTip> safetyTips = new ArrayList<SafetyTip>();
        safetyTips.add(safetyTip1);
        safetyTips.add(safetyTip2);

        when(safetyTipRepository.findByCategoryId(1L)).thenReturn(safetyTips);

        List<SafetyTip> foundSafetyTips = safetyTipService.getSafetyTipsByCategoryId(1L);

        assertThat(foundSafetyTips.size(), equalTo(2));
    }

    @Test
    public void getSafetyTipsByCategoryIdReturnsParentCategorySafetyTipsWhenNoExplicitCategorySafetyTips() {

        SafetyTip safetyTip1 = new SafetyTip();
        SafetyTip safetyTip2 = new SafetyTip();
        List<SafetyTip> safetyTips = new ArrayList<SafetyTip>();
        safetyTips.add(safetyTip1);
        safetyTips.add(safetyTip2);

        when(categoryService.getById(anyLong())).thenReturn(Optional.<Category>absent());
        Category parent = new Category();
        parent.setId(1L);
        when(categoryService.getById(1L)).thenReturn(Optional.of(parent));

        Category category = new Category();
        category.setId(2L);
        category.setParentId(1L);
        when(categoryService.getById(2L)).thenReturn(Optional.of(category));

        when(safetyTipRepository.findByCategoryId(1L)).thenReturn(safetyTips);

        List<SafetyTip> foundSafetyTips = safetyTipService.getSafetyTipsByCategoryId(2L);

        assertThat(foundSafetyTips.size(), equalTo(2));
    }
}
