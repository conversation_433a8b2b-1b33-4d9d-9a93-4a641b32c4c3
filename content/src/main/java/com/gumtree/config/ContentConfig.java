package com.gumtree.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

/**
 * Spring configuration for content module
 */
@Configuration
@ComponentScan(basePackages = {"com.gumtree.content", "com.gumtree.common.util.delimited" })
public class ContentConfig {

    @Bean
    public Resource safetyTipData() {
        return new ClassPathResource("data/safetyTipData.csv");
    }
}
