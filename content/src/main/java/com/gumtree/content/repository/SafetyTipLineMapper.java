package com.gumtree.content.repository;

import com.gumtree.common.util.delimited.DelimitedLineMapper;
import com.gumtree.content.domain.SafetyTip;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 */
@Component
public class SafetyTipLineMapper implements DelimitedLineMapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(SafetyTipLineMapper.class);

    @Override
    public SafetyTip map(String[] line) {
        SafetyTip safetyTip = null;
        try {
            safetyTip = new SafetyTip(Long.parseLong(line[0]), Integer.parseInt(line[1]), line[2]);
        } catch (NumberFormatException e) {
            LOGGER.error("Unable to parse SafetyTip", e);
        }
        return safetyTip;
    }
}
