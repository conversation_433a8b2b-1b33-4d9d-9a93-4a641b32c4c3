package com.gumtree.seller.qa.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import org.apache.commons.lang.Validate;

import java.io.IOException;
import java.time.Duration;

public class SellerHttpClient {
    private static final ObjectMapper MAPPER = objectMapper();

    private final String sellerHost;
    private final OkHttpClient client;

    public SellerHttpClient(String sellerHost) {
        this.sellerHost = sellerHost;

        HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
        logging.setLevel(HttpLoggingInterceptor.Level.BODY);
        this.client = new OkHttpClient.Builder()
                .readTimeout(Duration.ofSeconds(10))
                .addInterceptor(logging)
                .build();
    }

    public <T> T get(String url, TypeReference<T> clazz) throws IOException {
        Request request = new Request.Builder()
                .url(sellerHost + url)
                .addHeader("x-gt-get-model", "gumtree")
                .addHeader("x-forwarded-for", "127.0.0.1")
                .build();

        var call = client.newCall(request);
        try (Response response = call.execute()) {
            Validate.isTrue(response.code() == 200);
            String body = response.body().string();
            return MAPPER.readValue(body, clazz);
        }
    }

    public <T> T post(String url, TypeReference<T> clazz, RequestBody body) throws IOException {
        Request request = new Request.Builder()
                .url(sellerHost + url)
                .method("POST", body)
                .addHeader("x-gt-get-model", "gumtree")
                .addHeader("x-forwarded-for", "127.0.0.1")
                .build();

        var call = client.newCall(request);
        try (Response response = call.execute()) {
            Validate.isTrue(response.code() == 200);
            String responseBody = response.body().string();
            return MAPPER.readValue(responseBody, clazz);
        }
    }

    private static ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.LOWER_CAMEL_CASE);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.registerModule(new Jdk8Module());
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE);
        return objectMapper;
    }
}
