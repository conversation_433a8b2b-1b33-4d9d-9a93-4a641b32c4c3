package com.gumtree.seller.qa.postad;

import com.gumtree.seller.qa.BaseTest;
import com.gumtree.seller.qa.domain.AdvertAttributes;
import com.gumtree.seller.qa.domain.KnownCategories;
import com.gumtree.seller.qa.domain.TDSFixtures;
import com.gumtree.testdata.service.model.Ad;
import com.gumtree.testdata.service.model.ApiAttribute;
import com.gumtree.testdata.service.model.Attribute;
import com.gumtree.testdata.service.model.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

public class SYITest extends BaseTest {

    public static final String JACK_RUSSELL = "jack_russell";
    public static final String BRITISH_SHORTHAIR = "british_shorthair";

    private Ad advert;

    @BeforeEach
    public void setup() {
        advert = null;
    }

    @Test
    public void dogBreedTest() {
        // Given
        Long accountId = accountCreated(true);

        // When
        await().atMost(90, TimeUnit.SECONDS).until(advertCreated(accountId, KnownCategories.DOGS,
                decorateWithPetsAttributes(AdvertAttributes.DOG_BREED, JACK_RUSSELL)));

        // Then
        assertThat(advert).isNotNull();
        assertThat(advert.getId()).isNotNull();
        assertThat(advert.getAttributes()).contains(expectedAttribute(AdvertAttributes.DOG_BREED, JACK_RUSSELL));
    }

    @Test
    public void catBreedTest() {
        // Given
        Long accountId = accountCreated(true);

        // When
        await().atMost(90, TimeUnit.SECONDS).until(advertCreated(accountId, KnownCategories.CATS,
                decorateWithPetsAttributes(AdvertAttributes.CAT_BREED, BRITISH_SHORTHAIR)));

        // Then
        assertThat(advert).isNotNull();
        assertThat(advert.getId()).isNotNull();
        assertThat(advert.getAttributes()).contains(expectedAttribute(AdvertAttributes.CAT_BREED, BRITISH_SHORTHAIR));
    }

    @Test
    public void postFreebieItemTest() {
        // Given
        Long accountId = accountCreated(false);

        // When
        await().atMost(60, TimeUnit.SECONDS).until(advertCreated(accountId, KnownCategories.FREEBIES,
                Collections.emptyList()));

        // Then
        assertThat(advert).isNotNull();
        assertThat(advert.getId()).isNotNull();
        assertThat(advert.getTitle()).containsIgnoringCase(TEST_TITLE);
        assertThat(advert.getDescription()).isEqualTo(TEST_DESCRIPTION);
    }

    private Long accountCreated(boolean isPro) {
        User user = createUser(isPro);
        return user.getAccountIds().get(0);
    }

    private List<ApiAttribute> decorateWithPetsAttributes(String breed, String value) {
        List<ApiAttribute> attributes = new ArrayList<>();
        attributes.add(constructAttribute(AdvertAttributes.PET_READY_TO_LEAVE_DATE, "*************"));
        attributes.add(constructAttribute(AdvertAttributes.PET_DOB,"*************"));
        attributes.add(constructAttribute(AdvertAttributes.PET_REHOME_READY_DATE,"********"));
        attributes.add(constructAttribute(AdvertAttributes.PRICE,"19900"));
        attributes.add(constructAttribute(breed, value));
        attributes.add(constructAttribute(AdvertAttributes.PET_DATE_OF_BIRTH,"********"));
        return attributes;
    }

    private ApiAttribute constructAttribute(String name, String value) {
        ApiAttribute apiAttribute = new ApiAttribute();
        apiAttribute.setName(name);
        apiAttribute.setValue(value);
        return apiAttribute;
    }

    private Attribute expectedAttribute(String name, String value) {
        Attribute expected = new Attribute();
        expected.setName(name);
        expected.setValue(value);
        return expected;
    }

    private Callable<Boolean> advertCreated(Long accountId, Long categoryId, List<ApiAttribute> attributes) {
        Ad advert = createAdvert(TDSFixtures.customAdvert(accountId, categoryId, null, TEST_TITLE, TEST_DESCRIPTION, attributes));
        this.advert = advert;
        return () -> advert.getId() != null;
    }
}
