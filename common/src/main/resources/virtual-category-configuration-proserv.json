{"name": "Competitions", "fixed_url": "http://www.gumtreecompetitions.com", "id": 1000003, "seo_name": "competitions", "parent_id": 1, "sort_group_index": 7, "l1_category_id": 1000003, "level": 1, "children": [{"name": "Electronics & Gadgets", "fixed_url": "http://www.gumtreecompetitions.com/category.jsp?category=electronics", "id": 1000004, "seo_name": "competitions-electronics-gadgets", "parent_id": 1000003, "l1_category_id": 1000003, "level": 2}, {"name": "Lifestyle & Leisure", "fixed_url": "http://www.gumtreecompetitions.com/category.jsp?category=lifestyle", "id": 1000005, "seo_name": "competitions-lifestyle-leisure", "parent_id": 1000003, "l1_category_id": 1000003, "level": 2}, {"name": "Money", "fixed_url": "http://www.gumtreecompetitions.com/category.jsp?category=money", "id": 1000006, "seo_name": "competitions-money", "parent_id": 1000003, "l1_category_id": 1000003, "level": 2}, {"name": "Motoring", "fixed_url": "http://www.gumtreecompetitions.com/category.jsp?category=motoring", "id": 1000007, "seo_name": "competitions-motoring", "parent_id": 1000003, "l1_category_id": 1000003, "level": 2}, {"name": "Travel & Holidays", "fixed_url": "http://www.gumtreecompetitions.com/category.jsp?category=travel", "id": 1000008, "seo_name": "competitions-travel-holidays", "parent_id": 1000003, "l1_category_id": 1000003, "level": 2}]}