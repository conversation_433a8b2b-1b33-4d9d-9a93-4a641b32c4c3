package com.gumtree.domain.location;

import java.util.ArrayList;
import java.util.List;

/**
 * A list of UK cities,originally coming from the homepage
 */
public enum UkCities {

    LONDON("london"),
    READING("reading"),
    milton_keynes("milton-keynes"),
    BRIGHTON("brighton"),
    PORTSMOUTH("portsmouth"),
    SOUTHAMPTON("southampton"),
    OXFORD("oxford"),
    GUILDFORD("guildford"),
    BRISTOL("bristol"),
    PLYMOUTH("plymouth"),
    EXETER("exeter"),
    BOURNEMOUTH("bournemouth"),
    GLOUCESTER("gloucester"),
    BATH("bath"),
    SWINDON("swindon"),
    LUTON("luton"),
    CAMBRIDGE("cambridge"),
    NORWICH("norwich"),
    IPSWICH("ipswich"),
    NEWCASTLE("newcastle"),
    SUNDERLAND("sunderland"),
    MANCHESTER("manchester"),
    LIVERPOOL("liverpool"),
    DERBY("derby"),
    LEICESTER("leicester"),
    NOTTINGHAM("nottingham"),
    STOKE_ON_TRENT("stoke-on-trent"),
    BIRMINGHAM("birmingham"),
    COVENTRY("coventry"),
    HULL("hull"),
    MIDDLESBROUGH("middlesbrough"),
    YORK("york"),
    SHEFFIELD("sheffield"),
    BRADFORD("bradford"),
    LEEDS("leeds"),
    ABERDEEN("aberdeen"),
    DUNDEE("dundee"),
    EDINBURGH("edinburgh"),
    GLASGOW("glasgow"),
    INVERNESS("inverness"),
    CARDIFF("cardiff"),
    SWANSEA("swansea"),
    BELFAST("belfast");

    private String displayName;

    private UkCities(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public static String[] getAllDisplayNames() {
        List<String> allCities = new ArrayList<String>();
        for (UkCities city : UkCities.values()) {
            allCities.add(city.getDisplayName());
        }
        return allCities.toArray(new String[allCities.size()]);
    }
}
