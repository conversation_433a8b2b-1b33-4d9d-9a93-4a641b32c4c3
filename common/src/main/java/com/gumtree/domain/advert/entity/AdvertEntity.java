package com.gumtree.domain.advert.entity;

import com.gumtree.domain.advert.Advert;
import com.gumtree.domain.advert.AdvertStatus;
import com.gumtree.domain.advert.Price;
import com.gumtree.domain.feed.Feed;
import com.gumtree.domain.location.LocationCentroid;
import com.gumtree.domain.location.entity.LocationCentroidEntity;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.media.Video;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.user.User;
import com.gumtree.domain.user.entity.UserEntity;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Default implementation of {@link Advert}
 * <p/>
 * NOT THREAD SAFE! (see {@link #setAttributes(java.util.Collection)}}
 */
public class AdvertEntity implements Advert {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String title;
    private String description;
    private Integer locationId;
    private List<Integer> locationIds;
    private Long categoryId;
    private LocationCentroidEntity point;
    private String locationText;
    private String postcode;
    private PriceEntity price;
    private AdvertStatus status;
    private Image mainImage;
    private List<Image> images = new ArrayList<Image>();
    private List<Video> videos = new ArrayList<Video>();
    private Map<String, Attribute> attributeMap = new HashMap<String, Attribute>();
    private boolean visibleOnMap = false;
    private Date liveDate;
    private Date archivedDate;
    private UserEntity postingUser;
    private boolean urgent = false;
    private boolean featured = false;
    private boolean spotlighted = false;
    private Feed feed;
    private String websiteLink;
    private Boolean paidFor;
    private Boolean proAccountAd;

    /**
     * Default empty constructor
     */
    public AdvertEntity() {

    }

    @Override
    public final Long getId() {
        return id;
    }

    public final void setId(Long id) {
        this.id = id;
    }

    @Override
    public final String getTitle() {
        return title;
    }

    public final void setTitle(String title) {
        this.title = title;
    }

    @Override
    public final String getDescription() {
        return description;
    }

    public final void setDescription(String description) {
        this.description = description;
    }

    public final Integer getLocationId() {
        return locationId;
    }

    public final List<Integer> getLocationIds() {
        return locationIds;
    }

    public final void setLocationId(Integer locationId) {
        this.locationId = locationId;
    }

    public final Long getCategoryId() {
        return categoryId;
    }

    public final void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    @Override
    public final LocationCentroid getPoint() {
        return point;
    }

    public final void setPoint(LocationCentroidEntity point) {
        this.point = point;
    }

    @Override
    public final boolean isVisibleOnMap() {
        return visibleOnMap;
    }

    public final void setVisibleOnMap(boolean visibleOnMap) {
        this.visibleOnMap = visibleOnMap;
    }

    @Override
    public final String getLocationText() {
        return locationText;
    }

    public final void setLocationText(String locationText) {
        this.locationText = locationText;
    }

    @Override
    public final String getPostcode() {
        return postcode;
    }

    public final void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    @Override
    public final Price getPrice() {
        return price;
    }

    public final void setPrice(PriceEntity price) {
        this.price = price;
    }

    @Override
    public final AdvertStatus getStatus() {
        return status;
    }

    public final void setStatus(AdvertStatus status) {
        this.status = status;
    }

    @Override
    public final Image getMainImage() {
        return mainImage;
    }

    public final void setMainImage(Image mainImage) {
        this.mainImage = mainImage;
    }

    @Override
    public final Collection<Image> getImages() {
        return new ArrayList<Image>(images);
    }

    public final void setImages(List<Image> images) {
        this.images = images;
    }

    @Override
    public final Collection<Video> getVideos() {
        return new ArrayList<Video>(videos);
    }

    public final void setVideos(List<Video> videos) {
        this.videos = videos;
    }

    @Override
    public final Collection<Attribute> getAttributes() {
        return new ArrayList<Attribute>(attributeMap.values());
    }

    /**
     * @param attributes to be set
     */
    public final void setAttributes(Collection<Attribute> attributes) {
        attributeMap = new HashMap<String, Attribute>();
        for (Attribute attribute : attributes) {
            attributeMap.put(attribute.getType(), attribute);
        }
    }

    @Override
    public final Attribute getAttribute(String attributeTypeName) {
        return attributeMap.get(attributeTypeName);
    }

    @Override
    public final Date getLiveDate() {
        return liveDate;
    }

    public final void setLiveDate(Date liveDate) {
        this.liveDate = liveDate;
    }

    @Override
    public final Date getArchivedDate() {
        return archivedDate;
    }

    public final void setArchivedDate(Date archivedDate) {
        this.archivedDate = archivedDate;
    }

    @Override
    public final User getPostingUser() {
        return postingUser;
    }

    public final void setPostingUser(UserEntity postingUser) {
        this.postingUser = postingUser;
    }

    public void setSpotlighted(boolean spotlighted) {
        this.spotlighted = spotlighted;
    }

    @Override
    public boolean isSpotlighted() {
        return spotlighted;
    }

    @Override
    public final boolean isUrgent() {
        return urgent;
    }

    public final void setUrgent(boolean urgent) {
        this.urgent = urgent;
    }

    @Override
    public final boolean isFeatured() {
        return featured;
    }

    public final void setFeatured(boolean featured) {
        this.featured = featured;
    }

    @Override
    public final Feed getFeed() {
        return feed;
    }

    public final void setFeed(Feed feed) {
        this.feed = feed;
    }

    @Override
    public final String getWebsiteLink() {
        return websiteLink;
    }

    public final void setWebsiteLink(String websiteLink) {
        this.websiteLink = websiteLink;
    }

    @Override
    public final String toString() {
        return "AdvertEntity{"
                + "id=" + id
                + ", title='" + title + '\''
                + '}';
    }

    public final void setLocationIds(List<Integer> locationList) {
        this.locationIds = locationList;

    }

    public Boolean isPaidFor() {
        return paidFor;
    }

    public void setPaidFor(Boolean paidFor) {
        this.paidFor = paidFor;
    }

    public Boolean isProAccountAd() {
        return proAccountAd;
    }

    public void setProAccountAd(Boolean proAccountAd) {
        this.proAccountAd = proAccountAd;
    }

    public static final class Builder {
        private AdvertEntity advertEntity;

        private Builder() {
            advertEntity = new AdvertEntity();
        }

        public Builder withId(Long id) {
            advertEntity.id = id;
            return this;
        }

        public Builder withTitle(String title) {
            advertEntity.title = title;
            return this;
        }

        public Builder withDescription(String description) {
            advertEntity.description = description;
            return this;
        }

        public Builder withLocationId(Integer locationId) {
            advertEntity.locationId = locationId;
            return this;
        }

        public Builder withLocationIds(List<Integer> locationIds) {
            advertEntity.locationIds = locationIds;
            return this;
        }

        public Builder withCategoryId(Long categoryId) {
            advertEntity.categoryId = categoryId;
            return this;
        }

        public Builder withPoint(LocationCentroidEntity point) {
            advertEntity.point = point;
            return this;
        }

        public Builder withLocationText(String locationText) {
            advertEntity.locationText = locationText;
            return this;
        }

        public Builder withPostcode(String postcode) {
            advertEntity.postcode = postcode;
            return this;
        }

        public Builder withPrice(PriceEntity price) {
            advertEntity.price = price;
            return this;
        }

        public Builder withStatus(AdvertStatus status) {
            advertEntity.status = status;
            return this;
        }

        public Builder withMainImage(Image mainImage) {
            advertEntity.mainImage = mainImage;
            return this;
        }

        public Builder withImages(List<Image> images) {
            advertEntity.images = images;
            return this;
        }

        public Builder withVideos(List<Video> videos) {
            advertEntity.videos = videos;
            return this;
        }

        public Builder withAttributeMap(Map<String, Attribute> attributeMap) {
            advertEntity.attributeMap = attributeMap;
            return this;
        }

        public Builder withVisibleOnMap(boolean visibleOnMap) {
            advertEntity.visibleOnMap = visibleOnMap;
            return this;
        }

        public Builder withLiveDate(Date liveDate) {
            advertEntity.liveDate = liveDate;
            return this;
        }

        public Builder withPostingUser(UserEntity postingUser) {
            advertEntity.postingUser = postingUser;
            return this;
        }

        public Builder withUrgent(boolean urgent) {
            advertEntity.urgent = urgent;
            return this;
        }

        public Builder withFeatured(boolean featured) {
            advertEntity.featured = featured;
            return this;
        }

        public Builder withSpotlighted(boolean spotlighted) {
            advertEntity.spotlighted = spotlighted;
            return this;
        }

        public Builder withFeed(Feed feed) {
            advertEntity.feed = feed;
            return this;
        }

        public Builder withWebsiteLink(String websiteLink) {
            advertEntity.websiteLink = websiteLink;
            return this;
        }

        public Builder withPaidFor(Boolean paidFor) {
            advertEntity.paidFor = paidFor;
            return this;
        }

        public Builder withProAccountAd(Boolean proAccountAd) {
            advertEntity.proAccountAd = proAccountAd;
            return this;
        }

        public static Builder entity() {
            return new Builder();
        }

        public AdvertEntity build() {
            return advertEntity;
        }
    }
}
