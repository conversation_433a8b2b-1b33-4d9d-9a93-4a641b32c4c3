package com.gumtree.domain.user;

import com.gumtree.domain.Identifiable;

import java.util.Date;

/**
 * Represents an advert posting user.
 */
public interface User extends Identifiable<String> {

    /**
     * @return the user type
     */
    UserType getType();

    /**
     * @return the first date this user posted an advert
     */
    Date getFirstPostingDate();

    /**
     * @return the contact email address
     */
    String getEmailAddress();

    /**
     * @return the contact url
     */
    String getContactUrl();

    /**
     * @return the contact url
     */
    String getContactTelephone();


    /**
     * @return the contact telephone
     */
    String getContactName();


}
