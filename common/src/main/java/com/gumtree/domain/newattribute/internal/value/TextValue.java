package com.gumtree.domain.newattribute.internal.value;

import com.gumtree.domain.newattribute.AttributeValue;
import org.apache.commons.lang.WordUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * Test value.
 *
 * If you change something don't forget to overwrite equals and hashcode.
 *
 * <AUTHOR>
 *
 */
public final class TextValue implements AttributeValue {
    private final List<String> values;
    private final boolean isDisplayValue;

    /**
     * @param values list, string array
     * @param isDisplayValue true if value is for being displayed as it is
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    private TextValue(List<String> values, boolean isDisplayValue) {
        this.isDisplayValue = isDisplayValue;
        this.values = values;
    }

    @Override
    public String getName() {
        StringBuilder sb = new StringBuilder();
        for (Iterator<String> i = values.iterator(); i.hasNext();) {
            String value = i.next();
            sb.append(value);
            if (i.hasNext()) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T as(Class<T> clazz) {
        if (String.class.isAssignableFrom(clazz)) {
            return (T) getName();
        }

        if (List.class.isAssignableFrom(clazz)) {
            return (T) values;
        }

        if (clazz.isEnum()) {
            if (!values.isEmpty()) {
                @SuppressWarnings("rawtypes")
                Class enumClass = clazz;
                return (T) Enum.valueOf(enumClass, values.get(0).toUpperCase());
            }
        }
        return null;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        for (Iterator<String> i = values.iterator(); i.hasNext();) {
            String value = i.next();
            appendDefaultDisplayName(value, sb);
            if (i.hasNext()) {
                sb.append(", ");
            }
        }

        return sb.toString();
    }

    private void appendDefaultDisplayName(String str, StringBuilder displayName) {
        String word = isDisplayValue
                ? str : WordUtils.capitalizeFully(str.replaceAll("-", " ").replaceAll("_", " ").trim());
        displayName.append(word);
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((values == null) ? 0 : values.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof TextValue)) {
            return false;
        }
        TextValue other = (TextValue) obj;
        if (values == null) {
            if (other.values != null) {
                return false;
            }
        } else if (!values.equals(other.values)) {
            return false;
        }
        return true;
    }

    /**
     * Creates TextValue attribute value
     *
     * @param o
     *            - The object o
     * @param isDisplayValue
     *            - should be true if this value is for display.
     * @return - TextValue instance
     */
    public static AttributeValue create(Object o, boolean isDisplayValue) {
        List<String> values;
        if (o instanceof List) {
            values = (List) o;
        } else if (o instanceof String[]) {
            String[] strs = (String[]) o;
            values = Arrays.asList(strs);
        } else {
            values = Collections.singletonList(o.toString());
        }

        return new TextValue(values, isDisplayValue);
    }
}
