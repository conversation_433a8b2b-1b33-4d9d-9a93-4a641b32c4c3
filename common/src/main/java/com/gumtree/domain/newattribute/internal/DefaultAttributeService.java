package com.gumtree.domain.newattribute.internal;

import com.google.common.base.Optional;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.common.format.PriceFormatterImpl;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.AttributeService;
import com.gumtree.domain.newattribute.AttributeValue;
import com.gumtree.domain.newattribute.DisplayAttribute;
import com.gumtree.domain.newattribute.internal.value.BoolValue;
import com.gumtree.domain.newattribute.internal.value.DateValue;
import com.gumtree.domain.newattribute.internal.value.LongValue;
import com.gumtree.domain.newattribute.internal.value.TextValue;
import com.gumtree.domain.newattribute.internal.value.YearValue;
import com.gumtree.service.category.CategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.NumberFormat;
import java.text.ParseException;
import java.util.Locale;

/**
 * Default implementation.
 *
 * <AUTHOR>
 */
@Service
public class DefaultAttributeService implements AttributeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultAttributeService.class);

    private final CategoryService categoryService;

    @Autowired
    public DefaultAttributeService(CategoryService categoryService) {
        this.categoryService = categoryService;
    }

    @Override
    public final Attribute createAttribute(AttributeMetadata attributeMetadata, Object value) {
        AttributeValue attributeValue;
        try {
            switch (attributeMetadata.getType()) {
                case BOOLEAN:
                    attributeValue = BoolValue.create(value);
                    break;
                case DATETIME:
                    attributeValue = DateValue.create(value);
                    break;
                case INTEGER:
                case LONG:
                case CURRENCY:
                    if (!value.equals("")) {
                        if (CategoryConstants.Attribute.TICKET_FACE_VALUE.getName().equals(attributeMetadata.getName())) {
                            if (value instanceof String) {
                                double dVal = Integer.valueOf((String) value) / 100D;
                                attributeValue = TextValue.create(PriceFormatterImpl.formatWithZeros(dVal), true);
                            } else if (value instanceof Integer) {
                                double dVal = (Integer)value / 100D;
                                attributeValue = TextValue.create(PriceFormatterImpl.formatWithZeros(dVal), true);
                            } else {
                                attributeValue = TextValue.create(String.valueOf(value), true);
                            }
                        } else {
                            attributeValue = LongValue.create(value, attributeMetadata.getUnit());
                        }
                    } else {
                        attributeValue = LongValue.create("0", attributeMetadata.getUnit());
                    }
                    break;
                case STRING:
                case ENUM:
                    attributeValue = TextValue.create(value, true);
                    break;
                case YEAR:
                    attributeValue = YearValue.create(value);
                    break;
                default:
                    throw new IllegalArgumentException("Attribute type " + attributeMetadata + " is not mapped.");
            }
        } catch (InvalidAttributeValueException e) {
            LOGGER.info("Error converting the attribute. Type: [" + attributeMetadata + "], Value: [" + value + "]", e);
            return null;
        }

        return new DefaultAttribute(attributeMetadata.getName(), attributeValue);
    }

    @Override
    public Optional<Attribute> createAttribute(String attributeName, Long categoryId, String attributeValue) {
        Optional<Category> categoryOption = categoryService.getById(categoryId);
        if (categoryOption.isPresent()) {
            Optional<AttributeMetadata> attributeMetadata = categoryOption.get().findAttribute(attributeName);
            if (attributeMetadata.isPresent()) {
                return Optional.fromNullable(createAttribute(attributeMetadata.get(), attributeValue));
            }
        }
        return Optional.<Attribute>absent();
    }

    @Override
    public final Optional<String> getAttributeTypeDisplayName(String attributeName, Long categoryId) {
        return categoryId != null
            ? categoryService.attributeDisplayLabel(categoryId, attributeName)
            : Optional.<String>absent();
    }

    @Override
    public final Optional<DisplayAttribute> getDisplayAttribute(Attribute attribute, Long categoryId) {
        String valueDisplayName = getAttributeValueDisplayName(attribute, categoryId);
        Optional<String> typeDisplayName = getAttributeTypeDisplayName(attribute.getType(), categoryId);
        return typeDisplayName.isPresent()
                ? Optional.fromNullable(new DisplayAttribute(attribute.getValue().getName(), valueDisplayName, typeDisplayName.get()))
                : Optional.<DisplayAttribute>absent();
    }

    private String format(Attribute attribute, String displayName) {
        if (attribute.getType().equals(CategoryConstants.Attribute.TICKET_FACE_VALUE.getName())) {
        //TODO: add formatting information to Category API !!!!
            return PriceFormatterImpl.formatWithZeros(parseDouble(displayName) / 100);
        }
        return displayName;
    }

    @Override
    public String getAttributeValueDisplayName(Attribute attribute, Long actualCategory) {
        Optional<String> displayName = categoryService.attributeValueDisplayLabel(
                actualCategory, attribute.getType(), attribute.getValue().getName());
        String displayNameLabel = (displayName.isPresent()) ? displayName.get() : attribute.getValue().toString();
        return format(attribute, displayNameLabel);
    }

    @Override
    public final boolean isSupportedByCategory(String attributeName, Long categoryId) { //TODO <-- use name instead of meta ?
        return categoryService.supportsAttribute(categoryId, attributeName);
    }

    private Double parseDouble(String str) {
        NumberFormat numberFormat = NumberFormat.getIntegerInstance(Locale.ENGLISH);
        try {
            return numberFormat.parse(str).doubleValue();
        } catch (ParseException e) {
            return 0D;
        }
    }
}
