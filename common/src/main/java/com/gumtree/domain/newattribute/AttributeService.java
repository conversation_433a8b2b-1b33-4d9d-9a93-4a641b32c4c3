package com.gumtree.domain.newattribute;

import com.google.common.base.Optional;
import com.gumtree.api.category.domain.AttributeMetadata;

/**
 * Responsible for handling all attribute related tasks.
 * <AUTHOR>
 *
 */
public interface AttributeService {

    /**
     * Determines whether or not a certain category supports a certain attribute type.
     * @param categoryId to check
     * @return true or false
     */
    boolean isSupportedByCategory(String attributeName, Long categoryId);

    /**
     * Creates an attribute.
     * @param attributeMetadata the attribute type with related information
     * @param value the value
     * @return the new attribute instance
     */
    Attribute createAttribute(AttributeMetadata attributeMetadata, Object value);

    Optional<Attribute> createAttribute(String attributeName, Long categoryId, String attributeValue);

    /**
     * Get the display name of an attribute type depending on a category.
     *
     * @param attributeName not null
     * @param categoryId  not null
     * @return the display name
     */
    Optional<String> getAttributeTypeDisplayName(String attributeName, Long categoryId);

    /**
     * Get the display attribute for an attribute depending on a category.
     * @param attribute created by this service instance
     * @param categoryId not null category id
     * @return new or cached instance
     * @throws IllegalArgumentException if categoryId is NULL
     */
    Optional<DisplayAttribute> getDisplayAttribute(Attribute attribute, Long categoryId);

    /**
     * Get the display attribute value for an attribute depending on a category.
     * @param attribute created by this service instance
     * @param categoryId not null category id
     * @return new or cached instance
     */
    String getAttributeValueDisplayName(Attribute attribute, Long categoryId);
}
