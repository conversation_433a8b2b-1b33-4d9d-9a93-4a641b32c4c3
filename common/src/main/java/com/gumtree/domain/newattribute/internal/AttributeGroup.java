package com.gumtree.domain.newattribute.internal;

import org.codehaus.jackson.map.annotate.JsonDeserialize;

import java.util.List;

/**
 */
public class AttributeGroup {

    @JsonDeserialize
    private String category;

    @JsonDeserialize
    private List<String> attributes;

    @JsonDeserialize
    private List<String> exclusions;

    public final String getCategory() {
        return category;
    }

    public final List<String> getAttributeIds() {
        return attributes;
    }

    public final List<String> getExclusionIds() {
        return exclusions;
    }
}
