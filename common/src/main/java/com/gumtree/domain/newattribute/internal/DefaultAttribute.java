package com.gumtree.domain.newattribute.internal;

import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.AttributeValue;

/**
 * Default implementation.
 * <AUTHOR>
 *
 */
public final class DefaultAttribute implements Attribute {
    private final String attributeType;
    private final AttributeValue attributeValue;

    /**
     * Default contructor
     * @param attributeType attributeType
     * @param attributeValue attributeValue
     */
    public DefaultAttribute(String attributeType, AttributeValue attributeValue) {
        this.attributeType = attributeType;
        this.attributeValue = attributeValue;
    }

    @Override
    public String getType() {
        return attributeType;
    }

    @Override
    public AttributeValue getValue() {
        return attributeValue;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result
                + ((attributeType == null) ? 0 : attributeType.hashCode());
        result = prime * result
                + ((attributeValue == null) ? 0 : attributeValue.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof DefaultAttribute)) {
            return false;
        }
        DefaultAttribute other = (DefaultAttribute) obj;
        if (attributeType == null) {
            if (other.attributeType != null) {
                return false;
            }
        } else if (!attributeType.equals(other.attributeType)) {
            return false;
        }
        if (attributeValue == null) {
            if (other.attributeValue != null) {
                return false;
            }
        } else if (!attributeValue.equals(other.attributeValue)) {
            return false;
        }
        return true;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return "DefaultAttribute [attributeType=" + attributeType
                + ", attributeValue=" + attributeValue + "]";
    }
}
