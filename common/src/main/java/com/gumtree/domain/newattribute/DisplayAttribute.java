package com.gumtree.domain.newattribute;

/**
 * Default implementation.
 * <AUTHOR>
 *
 */
public class DisplayAttribute {

    private final String valueName;
    private final String valueDisplayName;
    private final String typeDisplayName;

    /**
     * @param valueName servers as identifier
     * @param valueDisplayName for displaying
     * @param typeDisplayName for displaying
     */
    public DisplayAttribute(String valueName, String valueDisplayName, String typeDisplayName) {
        this.valueName = valueName;
        this.valueDisplayName = valueDisplayName;
        this.typeDisplayName = typeDisplayName;
    }

    public String getValue() {
        return valueName;
    }

    public String getDisplayValue() {
        return valueDisplayName;
    }

    public String getTypeDisplayName() {
        return typeDisplayName;
    }

    @Deprecated
    public String getId() {
        return getValue();
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result
                + ((typeDisplayName == null) ? 0 : typeDisplayName.hashCode());
        result = prime
                * result
                + ((valueDisplayName == null) ? 0 : valueDisplayName.hashCode());
        result = prime * result
                + ((valueName == null) ? 0 : valueName.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof DisplayAttribute)) {
            return false;
        }
        DisplayAttribute other = (DisplayAttribute) obj;
        if (typeDisplayName == null) {
            if (other.typeDisplayName != null) {
                return false;
            }
        } else if (!typeDisplayName.equals(other.typeDisplayName)) {
            return false;
        }
        if (valueDisplayName == null) {
            if (other.valueDisplayName != null) {
                return false;
            }
        } else if (!valueDisplayName.equals(other.valueDisplayName)) {
            return false;
        }
        if (valueName == null) {
            if (other.valueName != null) {
                return false;
            }
        } else if (!valueName.equals(other.valueName)) {
            return false;
        }
        return true;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return "DefaultDisplayAttribute [valueName=" + valueName
                + ", valueDisplayName=" + valueDisplayName
                + ", typeDisplayName=" + typeDisplayName + "]";
    }
}
