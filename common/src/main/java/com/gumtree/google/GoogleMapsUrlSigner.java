package com.gumtree.google;

import org.apache.shiro.codec.Base64;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

/**
 * Component for signing Google Maps urls.
 */
@Component
public class GoogleMapsUrlSigner implements InitializingBean {

    @Value("${gumtree.google.maps.key:HEfjRBVgKHdYoP6qXiuD1NRCCJM=}")
    private String encodedKey;

    // This variable stores the binary key, which is computed from the string (Base64) key
    private byte[] decodedKey;

    @Override
    public final void afterPropertiesSet() {
        // Convert the key from 'web safe' base 64 to binary
        String keyString = encodedKey.replace('-', '+');
        keyString = keyString.replace('_', '/');
        decodedKey = Base64.decode(keyString);
    }

    /**
     * Sign a request for Google Maps.
     * Copied from https://developers.google.com/maps/documentation/business/webservices#JavaSignatureExample
     * @param path the path section of the URL to be signed
     * @param query the query part of the URL to be signed
     * @return the full resource URL with signature
     * @throws UrlSigningException
     */
    public String signRequest(String path, String query) throws UrlSigningException {

        // Retrieve the proper URL components to sign
        String resource = path + '?' + query;

        Mac mac;

        try {
            // Get an HMAC-SHA1 signing key from the raw key bytes
            SecretKeySpec sha1Key = new SecretKeySpec(decodedKey, "HmacSHA1");

            // Get an HMAC-SHA1 Mac instance and initialize it with the HMAC-SHA1 key
            mac = Mac.getInstance("HmacSHA1");
            mac.init(sha1Key);
        } catch (Exception e) {
            throw new UrlSigningException(e);
        }

        // compute the binary signature for the request
        byte[] sigBytes = mac.doFinal(resource.getBytes());

        // base 64 encode the binary signature
        String signature = new String(Base64.encode(sigBytes));

        // convert the signature to 'web safe' base 64
        signature = signature.replace('+', '-');
        signature = signature.replace('/', '_');

        return resource + "&signature=" + signature;
    }
}
