package com.gumtree.util.helper;


public enum DisplayAdsViewMode {
    ADSENSE_F("testF_control", false, false),
    ADSENSE_G("testG_control", false, false),
    ADSENSE_GM("testG_control", false, false),
    TEXTLINK_ACTIVE("testG_group1", false, true),
    TEXTLINK_ACTIVE_FOR_SALE("testF_group2", false, true),
    ECN_ACTIVE("testF_group1", true, false),
    SPLIT1("tablet_smartphone_1", false, false),
    SPLIT2("tablet_smartphone_2", false, true),
    SPLIT3("tablet_smartphone_3", false, true),
    SPLIT4("tablet_smartphone_4", false, true),
    SPLIT5("tablet_smartphone_5", false, true),
    SPLIT6("tablet_smartphone_6", false, true),
    TABLET_ECN("tablet_ecn", true, false),
    STUBHUB("stubhub", false, false);

    private final String name;
    private final Boolean showEcn;
    private final Boolean showTextlink;

    private DisplayAdsViewMode(String name, Boolean showEcn, Boolean showTextlink) {
        this.name = name;
        this.showEcn = showEcn;
        this.showTextlink = showTextlink;
    }

    public String getName() {
        return name;
    }

    public Boolean getShowEcn() {
        return showEcn;
    }

    public Boolean getShowTextlink() {
        return showTextlink;
    }

    @Override
    public String toString() {
        return name;
    }
}
