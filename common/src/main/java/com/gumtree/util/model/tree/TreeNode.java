package com.gumtree.util.model.tree;

import com.gumtree.domain.Identifiable;

import java.util.List;

/**
 * Represents a node within a tree.
 *
 * @param <T> the model type of the node
 */
public interface TreeNode<T extends Identifiable> {

    /**
     * The id of this node - used in rendering contexts.
     *
     * @return the id of the node.
     */
    String getId();

    /**
     * Get the model stored in this node.
     *
     * @return the model stored in this node.
     */
    T getModel();

    /**
     * Get this node's parent.
     *
     * @return this node's parent - null if it has no parent.
     */
    TreeNode<T> getParent();

    /**
     * Get this node's children.
     *
     * @return this node's children.
     */
    List<TreeNode<T>> getChildren();
}
