package com.gumtree.util.model;

/**
 * Actions enums for various static pages
 */
public enum Actions implements Action {
    //todo: remove legacy stuff
    PRIVACY_POLICY("/privacy_policy"),
    UK_HOME("/uk"),
    PARTNERS("/partners"),
    USER_HOME("/"),
    HELP_PAGE("/help"),
    CONTACT_US("/contactus"),
    STAY_SAFE("/help/stay_safe"),
    PAGE_EXPIRED("/page-expired"),
    ABOUT_GUMTREE("http://blog.gumtree.com/aboutus/", true),
    BUSINESS_ADVERTISING("http://www.gumtreeforbusiness.co.uk", true),
    TERMS_OF_USE("/termsofuse"),
    DOWNLOAD_APPS("/apps"),
    INSURANCE("/insurance"),
    SITE_MAP("/sitemap"),
    POPULAR_SEARCHES("/popular-search"),
    FORUMS("http://forums.gumtree.com", true),
    BLOG("http://blog.gumtree.com", true),
    FACEBOOK("http://www.facebook.com/gumtree", true),
    TWITTER("http://twitter.com/gumtree", true),
    SAVED_ADS("/my-account/favourites"),
    SAVED_SEARCHES("/my-account/saved-searches"),
    CREATE_ACCOUNT("/create-account", ApplicationEndpoint.SELLER_SECURE),
    LOGOUT("/account/logout", ApplicationEndpoint.SELLER),
    BUSHFIRE_LOGIN("/login", ApplicationEndpoint.SELLER_SECURE),
    BUSHFIRE_LOGOUT("/logout", ApplicationEndpoint.SELLER),
    BUSHFIRE_MANAGE_ADS("/manage/ads", ApplicationEndpoint.SELLER_SECURE),
    BUSHFIRE_REPLIES_OUTBOX(URLS.BUSHFIRE_REPLIES_OUTBOX, ApplicationEndpoint.SELLER_SECURE),
    BUSHFIRE_REPLIES_INBOX(URLS.BUSHFIRE_REPLIES_INBOX, ApplicationEndpoint.SELLER_SECURE),
    BUSHFIRE_MANAGE_ACCOUNT("/manage-account/", ApplicationEndpoint.SELLER_SECURE),
    BUSHFIRE_MESSAGE_CENTRE("/manage/messages", ApplicationEndpoint.SELLER_SECURE),
    BUSHFIRE_POST_AD("/postad", ApplicationEndpoint.SELLER_SECURE),
    BUSHFIRE_RESET_PASSWORD("/reset-password", ApplicationEndpoint.SELLER_SECURE),
    BUSHFIRE_FORGOTTEN_PASSWORD("/forgotten-password", ApplicationEndpoint.SELLER_SECURE),
    BUSHFIRE_PACKAGE_USAGE("/manage/company/packages/usage", ApplicationEndpoint.SELLER_SECURE),
    SEARCH_POSTINGS("/cgi-bin/list_postings.pl"),
    SEARCH("/search"),
    MOBILE_SITE("http://m.gumtree.com", true),
    MOBILE_SITE_REDIRECT("/mobile_site.html"),
    SPOTLIGHT_HELP("http://gumtree.force.com/Help/knowledgeProduct?c=Homepage_Spotlight", true),
    LEGACY_REPORT_ACTION("/cgi-bin/report_ad.pl"),
    POSTING_RULES_PAGE("/help/posting_rules"),
    COOKIES("/cookies"),
    ABOC_FRAMEWORK("http://www.youronlinechoices.com/uk/", true),
    HELP_AT_GUMTREE("<EMAIL>", false, true),
    MANAGING_COOKIES("/accepting_deleting_cookies.html"),
    GFORCE_HELP("http://gumtree.force.com/Help", true),
    OLD_PRIVACY_POLICY("/privacypolicy2008"),
    GUMTREE_HOME("http://www.gumtree.com", true),
    GUMTREE_PETS_POLICY("http://gumtree.force.com/Help/articles/General_Information/Pets", true),
    GUMTREE_MASSAGES_POLICY("http://gumtree.force.com/Help/articles/General_Information/Business-Services", true),
    IPHONE_APP("http://itunes.apple.com/gb/app/gumtree/id487946174", true),
    TERMS_OF_USE_2008("/termsofuse2008"),
    TERMS_OF_USE_2013("/termsofuse2013"),
    TERMS_OF_USE_2016("/termsofuse2016"),
    TERMS_OF_USE_2019("/termsofuse2019"),
    NOTICE_OF_INFRINGEMENT_FORM("/pdf/noticeofinfringement.pdf"),
    BUSHFIRE_CREATE_PASSWORD("/create-password", ApplicationEndpoint.SELLER_SECURE),
    ANDROID_APP("https://play.google.com/store/apps/details?id=com.gumtree"
            + ".android#?t=W251bGwsMSwxLDIxMiwiY29tLmd1bXRyZWUuYW5kcm9pZCJd", true),
    MODERN_SLAVERY("/modern_slavery");

    private final String url;

    private ApplicationEndpoint endpoint;

    private boolean absolute = false;

    private boolean mailLink = false;

    Actions(String url) {
        this(url, Actions.ApplicationEndpoint.BUYER);
    }

    Actions(String url, boolean absolute) {
        this(url, null);
        this.absolute = absolute;
    }

    Actions(String url, boolean absolute, boolean mailLink) {
        this(url, null);
        this.absolute = absolute;
        this.mailLink = mailLink;
    }

    Actions(String url, ApplicationEndpoint endpoint) {
        this.url = url;
        this.endpoint = endpoint;
    }

    public String getUrl() {
        return url;
    }

    public ApplicationEndpoint getEndpoint() {
        return endpoint;
    }

    @Override
    public boolean isUrlAbsolute() {
        return absolute;
    }

    @Override
    public boolean isMailLink() {
        return mailLink;
    }

    /**
     * Urls used in the above actions
     * <p/>
     * This interface was create to reduce duplication of thos URLS our the code base
     */
    public static final class URLS {
        public static final String BUSHFIRE_REPLIES_OUTBOX = "/manage/replies/outbox";
        public static final String BUSHFIRE_REPLIES_INBOX = "/manage/replies/inbox";
    }
}
