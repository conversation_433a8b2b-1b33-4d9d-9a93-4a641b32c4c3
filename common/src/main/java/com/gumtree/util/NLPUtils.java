package com.gumtree.util;

import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class NLPUtils {
    private static final Pattern PUNCTUATION_PATTERN = Pattern.compile("[^a-zA-Z0-9\\s]");
    /**
     * tokenize the input text into words
     *
     * @param text               the input text
     * @param removePunctuation  whether remove punctuation
     * @param caseSensitive      whether sensitive to letter case
     * @return words set
     */
    public static List<String> tokenizeText(String text, boolean removePunctuation, boolean caseSensitive) {
        if (StringUtils.isBlank(text)) return Collections.emptyList();
        String processed = text.trim();
        if (removePunctuation) {
            processed = PUNCTUATION_PATTERN.matcher(processed).replaceAll(" ");
        }
        if (!caseSensitive) {
            processed = processed.toLowerCase(Locale.ROOT);
        }
        return Arrays.stream(processed.split("\\s+"))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * generate n-gram of the word level for the input text
     *
     * @param words             the input word list
     * @param n                 the length of gram
     * @return n-gram list
     */
    public static List<String> generateNGram(List<String> words, int n) {
        if (0 >= n) return Collections.emptyList();
        List<String> ngrams = new ArrayList<>();
        for (int i = 0; i <= words.size() - n; ++i) {
            ngrams.add(String.join(" ", words.subList(i, i + n)));
        }
        return ngrams;
    }

    public static boolean isEmpty(Collection<?> coll) {
        return coll == null || coll.isEmpty();
    }
}
