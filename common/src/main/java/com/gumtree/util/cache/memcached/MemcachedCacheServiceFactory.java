package com.gumtree.util.cache.memcached;

import com.gumtree.common.util.cache.remote.GumtreeCacheable;
import com.gumtree.util.cache.CacheService;
import com.gumtree.util.cache.CacheServiceFactory;
import com.gumtree.util.cache.CacheValueFactory;
import com.gumtree.util.cache.TypedCacheService;
import com.gumtree.util.cache.TypedCacheValueFactory;
import net.spy.memcached.MemcachedClientIF;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Properties;

/**
 * User: rajisingh
 * Date: 20/04/11
 * Time: 18:10
 */
@Component
public class MemcachedCacheServiceFactory implements CacheServiceFactory {

    private static final Logger LOGGER = LoggerFactory.getLogger(MemcachedCacheServiceFactory.class);

    @Autowired
    private MemcachedClientIF memcachedClient;

    @Autowired
    @Qualifier("packageProperties")
    private Properties packageProperties;

    // TODO: Set sensible production defaults

    @Value("${gumtree.memcache.expiry:900}")
    private int cacheExpiry;

    @Value("${gumtree.memcache.updaterExpiry:10}")
    private int updaterExpiry;

    @Value("${gumtree.memcache.refreshExpiry:600}")
    private int refreshExpiry;

    @Value("${gumtree.memcache.devProjectVersion:DEV}")
    private String devProjectVersion;

    private String projectVersion;

    @Override
    public <K extends GumtreeCacheable,T> TypedCacheService<K,T> getService(TypedCacheValueFactory<K,T> valueFactory) {
        TypedMemcachedCacheService<K,T> service = new TypedMemcachedCacheService<K,T>();
        service.setMemcachedClient(memcachedClient);
        service.setValueFactory(valueFactory);
        service.setCacheExpiry(cacheExpiry);
        service.setUpdaterExpiry(updaterExpiry);
        service.setRefreshExpiry(refreshExpiry);
        service.setProjectVersion(projectVersion);

        return service;
    }

    @Override
    public final <T> CacheService<T> getService(final CacheValueFactory<T> valueFactory) {
        MemcachedCacheService<T> service = new MemcachedCacheService<T>();
        service.setMemcachedClient(memcachedClient);
        service.setValueFactory(valueFactory);
        service.setCacheExpiry(cacheExpiry);
        service.setUpdaterExpiry(updaterExpiry);
        service.setRefreshExpiry(refreshExpiry);
        service.setProjectVersion(projectVersion);

        return service;
    }

    @PostConstruct
    private void loadVersionFromPackageProperties() throws IOException {
        String versionString = packageProperties.getProperty("version");
        projectVersion = StringUtils.hasLength(versionString) ? versionString : devProjectVersion;
        LOGGER.info("MemcachedCacheServiceFactory - Project Version: " + projectVersion);
    }
}
