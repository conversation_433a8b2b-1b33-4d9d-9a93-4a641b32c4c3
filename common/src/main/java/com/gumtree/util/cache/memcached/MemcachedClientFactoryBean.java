package com.gumtree.util.cache.memcached;

import net.spy.memcached.AddrUtil;
import net.spy.memcached.MemcachedClient;
import net.spy.memcached.MemcachedClientIF;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.AbstractFactoryBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Implements the Spring factory bean concept to create a {@link MemcachedClient}.
 */
public class MemcachedClientFactoryBean extends AbstractFactoryBean<MemcachedClientIF> {

    @Value("${gumtree.memcache.servers:localhost:11211}")
    private String memcacheServers;

    @Value("${gumtree.distributed_cache:stub}")
    private String distributedCache;

    private static List<MemcachedClientIF> memcachedClients;

    /**
     * Initialise factory bean - which includes creating an empty list of clients
     */
    public MemcachedClientFactoryBean() {
        memcachedClients = new ArrayList<MemcachedClientIF>();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final Class<?> getObjectType() {
        return MemcachedClientIF.class;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected final MemcachedClientIF createInstance() throws Exception {
        MemcachedClientIF newClient;
        if (distributedCache != null && distributedCache.equals("stub")) {
            newClient = new MemcachedClientStub();
        } else if (distributedCache == null || distributedCache.equals("memcached")) {
            newClient = new MemcachedClient(AddrUtil.getAddresses(memcacheServers));
        } else {
            // TODO: Use a suitable exception
            throw new IllegalArgumentException("gumtree.distributed_cache has an invalid value: " + distributedCache);
        }
        memcachedClients.add(newClient);
        return newClient;
    }

    /**
     * Tells the composed memcachedClient to end its dedicated IO thread so that the JVM can exit cleanly
     */
    public final void shutdown() {
        for (MemcachedClientIF client : memcachedClients) {
            if (client != null) {
                client.shutdown();
            }
        }
    }

}
