package com.gumtree.util.url;

import com.gumtree.api.category.domain.Category;
import com.gumtree.common.util.StringUtils;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class PopularSearchUrlScheme extends UrlSchemeBase {

    /**
     * Get a url for viewing a category listing page
     *
     * @param category the category to view
     * @param location the location selected alongside the category
     * @return a url for viewing a category listing page.
     */
    public String getFor(Category category, Location location) {
        StringBuilder builder = absoluteUrlBuilder();
        builder.append("/popular-search");

        if (category != null) {
            builder.append("/").append(StringUtils.sanitise(category.getSeoName()));
        } else {
            builder.append("/").append(Categories.ALL.getSeoName());
        }

        if (location != null) {
            builder.append("/").append(StringUtils.sanitise(location.getName()));
        }

        return builder.toString();
    }

    /**
     * Get a url for viewing a listing page for all categories.
     * @param location must not be null
     */
    public String getFor(Location location) {
        return getFor(null, location);
    }
}
