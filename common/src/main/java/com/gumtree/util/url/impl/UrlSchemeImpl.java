package com.gumtree.util.url.impl;

import com.ebay.ecg.eps.client.EpsImageUrl;
import com.gumtree.api.Ad;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.common.util.url.UrlBuilder;
import com.gumtree.domain.advert.Advert;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.media.ImageSize;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.model.Action;
import com.gumtree.util.model.Actions;
import com.gumtree.util.model.ReportAction;
import com.gumtree.util.url.CdnImageUrlProvider;
import com.gumtree.util.url.SRPUrlScheme;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.util.url.UrlSchemeBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Default implementation of {@link com.gumtree.util.url.UrlScheme} for the Gumtree Public Website.
 */
public class UrlSchemeImpl extends UrlSchemeBase implements UrlScheme {

    private static final Pattern IMAGE_DOMAIN_PATTERN = Pattern.compile("^http://([a-z0-9][a-z0-9])\\..*");
    private static final Pattern YOUTUBE_ID_PATTERN = Pattern.compile("http.*\\?v=([a-zA-Z0-9_\\-]+)(?:&.)?.*");
    private static final String VIDEO_THUMB_URL = "http://img.youtube.com/vi/";
    private static final String VIDEO_URL = "http://www.youtube.com/embed/";
    private static final String VIDEO_THUMB_IMAGE = "/2.jpg";
    private static final String VIDEO_URL_REL = "?rel=0&modestbranding=1&controls=0&wmode=transparent";
    private static final String EMPTY_STRING = "";
    private static final String POSTAD_RESOURCE = "/postad";

    @Value("${gumtree.image.domain.dev:http://is.thegumtree.com}")
    private String imageDomain;

    @Value("${gumtree.image.domains.count:4}")
    private int numberOfImageDomains;

    @Value("${gumtree.image.domains.threshold:1000000}")
    private int imageThreshold;

    @Value("${gumtree.urls.category_landing_pages.enabled:}")
    private List<String> categoryLandingPagesEnabled;

    @Value("${gumtree.stats.ps.link.enabled:true}")
    private boolean popularSearchEnabled;

    @Value("${gumtree.urls.userreports:legacy}")
    private String userReportsType;

    @Value("${gumtree.savedsearch.server.enabled:true}")
    private boolean savedSearchServerEnabled;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private LocationService locationService;

    @Autowired
    private SRPUrlScheme srpUrlScheme;

    @Autowired
    private CdnImageUrlProvider cdnImageUrlProvider;

    /**
     * {@inheritDoc}
     */
    @Override
    public final String urlFor(Location location) {
        Assert.notNull(location);

        if (location.getName().equals("uk")) {
            return urlFor(Actions.UK_HOME);
        }

        StringBuilder url = absoluteUrlBuilder();
        if (!location.isLanding()) {
            url.append("/all");
        }
        url.append("/");
        url.append(location.getName());
        return url.toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String urlForLocationId(Integer locationId) {
        Assert.notNull(locationId);
        Location location = locationService.getById(locationId);
        return urlFor(location);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String urlForHomepage() {
        return urlFor(Actions.GUMTREE_HOME);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String urlForCategory(Category category, Location location) {
        Assert.notNull(category);
        Assert.notNull(location);

        // only use the category landing page url for categories that are enabled
        if (!categoryLandingPagesEnabled.contains(category.getSeoName())) {
            return srpUrlScheme.getFor(category, location);
        }
        if (category.getUrl() != null) {
            return getFixedCategoryUrl(category, location);
        }

        StringBuilder url = absoluteUrlBuilder();
        url.append("/c/");
        url.append(category.getSeoName());
        if (!location.getName().equals("uk")) {
            url.append("/");
            url.append(location.getName());
        }
        return url.toString();
    }

    @Override
    public final String listingUrlForCategory(Category category, Location location) {
        Assert.notNull(category);
        Assert.notNull(location);

        // only use the category landing page url for categories that are enabled
        if (!categoryLandingPagesEnabled.contains(category.getSeoName())) {
            return srpUrlScheme.getFor(category, location);
        }

        if (category.getUrl() != null) {
            return getFixedCategoryUrl(category, location);
        }

        StringBuilder url = absoluteUrlBuilder();
        url.append("/");
        url.append(category.getSeoName());
        if (!location.getName().equals("uk")) {
            url.append("/");
            url.append(location.getName());
        }
        return url.toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String urlFor(Advert advert) {
        Assert.notNull(advert);
        return urlForAdvert(advert.getId(), advert.getTitle(), advert.getCategoryId().longValue());
    }

    @Override
    public final String urlFor(Ad ad) {
        Assert.notNull(ad);
        return urlForAdvert(ad.getId(), ad.getTitle(), ad.getCategoryId());
    }

    @Override
    public final String editUrlFor(Ad ad) {
        Assert.notNull(ad);
        StringBuilder url = absoluteUrlBuilder(Action.ApplicationEndpoint.SELLER_SECURE);
        url.append("/postad?advertId=").append(ad.getId());
        return url.toString();
    }

    @Override
    public final String restoreUrlFor(Ad ad) {
        Assert.notNull(ad);
        StringBuilder url = absoluteUrlBuilder(Action.ApplicationEndpoint.SELLER_SECURE);
        url.append("/manage/ads/restore/").append(ad.getId());
        return url.toString();
    }

    @Override
    public final String urlForAdvert(Long advertId, String advertTitle, Long categoryId) {
        Category rootCategory = categoryService.getRootCategory();
        Category adCategory = categoryService.getById(categoryId).or(rootCategory);
        StringBuilder url = absoluteUrlBuilder();

        return UrlBuilder.buildForAdvert(url, advertId, advertTitle, adCategory.getSeoName());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String thumbnailUrlFor(Image image) {
        if (image == null) {
            return EMPTY_STRING;
        }

        String thumbnailUr = thumbnailUrlFor(image.getBaseUrl());
        if (!EMPTY_STRING.equals(thumbnailUr)) {
            return thumbnailUr;
        }

        if (image.getId() != null) {
            StringBuilder url = new StringBuilder();
            url.append(getImageDomain(image.getId()));
            url.append("/image/moreadsthumb/");
            url.append(image.getId());
            url.append(".jpg");
            return url.toString();
        }

        return EMPTY_STRING;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String thumbnailUrlFor(String imageBaseUrl) {
        if (imageBaseUrl != null && imageBaseUrl.length() > 0) {
            return cdnImageUrlProvider.getImageUrl(imageBaseUrl, ImageSize.MAXITHUMB);
        }
        return EMPTY_STRING;
    }

    @Override
    public final String previewUrlFor(Image image) {
        if (image == null) {
            return EMPTY_STRING;
        }

        String previewUrl = previewUrlFor(image.getBaseUrl());
        if (!EMPTY_STRING.equals(previewUrl)) {
            return previewUrl;
        }

        if (image.getId() != null) {
            StringBuilder url = new StringBuilder();
            url.append(getImageDomain(image.getId()));
            url.append("/image/moreadsthumb/");
            url.append(image.getId());
            url.append(".jpg");
            return url.toString();
        }

        return EMPTY_STRING;
    }

    @Override
    public final String previewUrlFor(String imageBaseUrl) {
        if (imageBaseUrl != null && imageBaseUrl.length() > 0) {
            return cdnImageUrlProvider.getImageUrl(imageBaseUrl, ImageSize.PREVIEW);
        }
        return EMPTY_STRING;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final Map<ImageSize, String> urlsFor(Image image) {
        Map<ImageSize, String> urls = new HashMap<>();
        if (image == null) {
            return urls;
        }

        if (image.getBaseUrl() != null && image.getBaseUrl().length() > 0) {
            for (ImageSize imageSize : ImageSize.values()) {
                String url = cdnImageUrlProvider.getSecureImageUrl(image.getBaseUrl(), imageSize.getId());
                urls.put(imageSize, url);
            }
        } else if (image.getId() != null) {
            // legacy image urls
            urls.put(ImageSize.THUMB, createUrl(image, "/image/thumb/"));
            urls.put(ImageSize.MAIN, createUrl(image, "/image/big/"));
            urls.put(ImageSize.FULL, createUrl(image, "/image/extrabig/"));
        }
        return urls;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String urlFor(Action action) {
        // TODO: Remove this switch when the new user-report stuff is live
        if (action instanceof ReportAction && userReportsType.equalsIgnoreCase("legacy")) {
            return urlFor(Actions.LEGACY_REPORT_ACTION);
        } else if (action.isUrlAbsolute()) {
            return action.getUrl();
        } else if (action.isMailLink()) {
            return "mailto:" + action.getUrl();
        } else if (Actions.POPULAR_SEARCHES.equals(action) && !popularSearchEnabled) {
            return "";
        }
        return absoluteUrlBuilder(action.getEndpoint()).append(action.getUrl()).toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String urlForSearchPostings(String searchTerms, String searchLocation) {
        StringBuilder sb = absoluteUrlBuilder();
        try {
            sb.append(urlFor(Actions.SEARCH_POSTINGS));
            if (searchLocation != null) {
                sb.append("?search_location=").append(URLEncoder.encode(searchLocation, "utf8"));
            }

            if (searchTerms != null) {
                sb.append("&search_terms=").append(URLEncoder.encode(searchTerms, "utf8"));
            }

            return sb.toString();
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public final String bushfirePostAdUrlFor(Category category) {
        if (category != null && !Categories.ALL.is(category)) {
            StringBuilder url = new StringBuilder(urlFor(Actions.BUSHFIRE_POST_AD));
            url.append("?categoryId=").append(category.getId());
            return url.toString();
        } else {
            return urlFor(Actions.BUSHFIRE_POST_AD);
        }
    }

    @Override
    public final String bushfirePostAdUrlForCategoryId(Long categoryId) {
        if (categoryId == null) {
            return bushfirePostAdUrlFor(null);
        } else {
            return bushfirePostAdUrlFor(categoryService.getById(categoryId).orNull());
        }
    }

    @Override
    public final String bushfirePostEventUrl() {
        StringBuilder url = new StringBuilder(urlFor(Actions.BUSHFIRE_POST_AD));
        url.append("?categoryId=21");
        return url.toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String postAdUrl() {
        return getPostad();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String postAdUrlFor(Location location) {
        return getPostad();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String postAdUrlFor(Category category) {
        return getPostad();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String postAdUrlFor(Location location, Category category) {
        return getPostad();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String postEventUrlFor(Location location) {
        return getPostad();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String viewEventsUrlFor(Location location) {
        Assert.notNull(location);
        Category events = categoryService.getById(CategoryConstants.EVENTS_ID).get();
        return srpUrlScheme.getFor(events, location);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String rssEventsUrlFor(Location location) {
        Assert.notNull(location);
        StringBuilder url = absoluteUrlBuilder();
        url.append("/rssfeed/all/"); //unfortunately this is in common so we can't access route form public. Hardcoding
        url.append(location.getName());
        return url.toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String emailAlertsUrlFor(Long categoryId, Location location) {
        StringBuilder sb = new StringBuilder("http://alerts.gumtree.com/?");

        if (location != null) {
            Location landingLocation = locationService.getLanding(location);
            if (landingLocation != null) {
                try {
                    sb.append("search_location=").append(
                            URLEncoder.encode(landingLocation.getDisplayName(), "utf8"));
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }

                if (categoryId != null) {
                    sb.append("#cat-").append(categoryId);
                }
            }
        }

        return sb.toString();

    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String saveSearchUrl() {
        if (savedSearchServerEnabled) {
            StringBuilder url = absoluteUrlBuilder(Action.ApplicationEndpoint.SELLER_SECURE);
            url.append("/user/savedsearch/{savedSearchRequest}");
            return url.toString();
        } else {
            return "";
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String saveEmailAlertUrl() {
        if (savedSearchServerEnabled) {
            StringBuilder url = absoluteUrlBuilder(Action.ApplicationEndpoint.SELLER_SECURE);
            url.append("/user/activateemailalert/");
            return url.toString();
        } else {
            return "";
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String replyUrlFor(Advert advert) {
        StringBuilder url = absoluteUrlBuilder(Action.ApplicationEndpoint.REPLY);
        url.append("/reply?advertId=");
        url.append(advert.getId());
        return url.toString();
    }

    private String getImageDomain(Long imageId) {
        if (imageId <= imageThreshold) {
            return imageDomain;
        }

        Matcher matcher = IMAGE_DOMAIN_PATTERN.matcher(imageDomain);

        if (matcher.matches()) {
            String subDomainSegment = matcher.group(1);
            if (subDomainSegment != null) {
                Long domainNo = imageId % numberOfImageDomains;
                return imageDomain.replaceFirst(
                        subDomainSegment,
                        subDomainSegment + String.format("%02d", domainNo));
            }
        }

        return imageDomain;
    }

    private String createUrl(Image image, String logic) {
        StringBuilder url = new StringBuilder();
        url.append(getImageDomain(image.getId()));
        url.append((logic));
        url.append((image.getId()));
        url.append(".jpg");
        return url.toString();
    }

    /**
     * Url for fixed categories.
     *
     * @param category required
     * @param location required
     * @return the url
     */
    public static String getFixedCategoryUrl(Category category, Location location) {
        try {
            return category.getUrl().replaceAll("\\$location",
                    URLEncoder.encode(location.getDisplayName(), "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String urlForXMLSitemap(String pathPrefix, int page) {
        StringBuilder url = absoluteUrlBuilder();
        url.append(pathPrefix);
        url.append(String.format("/sitemap-%s.xml", page));

        return url.toString();
    }

    @Override
    public final String urlForYoutubeThumbnail(String youtubeUrl) {
        StringBuilder sb = new StringBuilder();
        Matcher m = YOUTUBE_ID_PATTERN.matcher(youtubeUrl);

        if (m.matches()) {
            String vidId = m.group(1);
            sb.append(VIDEO_THUMB_URL);
            sb.append(vidId);
            sb.append(VIDEO_THUMB_IMAGE);
        }

        return sb.toString();
    }

    @Override
    public final String urlForYoutubeEmbed(String youtubeUrl) {
        StringBuilder sb = new StringBuilder();
        Matcher m = YOUTUBE_ID_PATTERN.matcher(youtubeUrl);

        if (m.matches()) {
            String vidId = m.group(1);
            sb.append(VIDEO_URL);
            sb.append(vidId);
            sb.append(VIDEO_URL_REL);
        }

        return sb.toString();
    }

    @Override
    public String urlForPackageUsageHistory(long packageId) {
        StringBuilder url = absoluteUrlBuilder(Action.ApplicationEndpoint.SELLER_SECURE);
        url.append("/manage/company/packages/").append(packageId);
        return url.toString();
    }

    @Override
    public String urlForSellerAds(long originalAdvertId) {
        StringBuilder url = absoluteUrlBuilder();
        url.append("/sellerads/").append(originalAdvertId);

        return url.toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String urlToReportAd(long advertId) {
        StringBuilder url = absoluteUrlBuilder();
        url.append("/report-ad/").append(advertId);

        return url.toString();
    }

    private String getPostad() {
        StringBuilder url = absoluteUrlBuilder(Action.ApplicationEndpoint.SELLER);
        url.append(POSTAD_RESOURCE);
        return url.toString();
    }
}
