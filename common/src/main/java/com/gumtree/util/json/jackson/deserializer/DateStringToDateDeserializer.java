package com.gumtree.util.json.jackson.deserializer;

import com.gumtree.util.DateUtils;
import org.codehaus.jackson.JsonParser;
import org.codehaus.jackson.map.DeserializationContext;
import org.codehaus.jackson.map.JsonDeserializer;

import java.io.IOException;
import java.util.Date;

/**
 * Converts a date string in the format yyyyMMdd into a {@link java.util.Date}
 */
public class DateStringToDateDeserializer extends JsonDeserializer<Date> {

    @Override
    public final Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
            throws IOException {
        return DateUtils.convertDateString(jsonParser.getText(), "yyyyMMdd");
    }
}
