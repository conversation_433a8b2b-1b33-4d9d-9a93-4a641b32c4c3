package com.gumtree.search;

import com.gumtree.api.category.domain.Category;

import java.util.Collections;
import java.util.Set;

/**
 * Represents a keyword search.
 *
 * <AUTHOR>
 */
public class UserSearchKeywords {

    /**
     * empty search
     */
    public static final UserSearchKeywords EMPTY =
            new UserSearchKeywords("", Collections.<String>emptySet(), Collections.<Category>emptySet());

    private final Set<Category> categories;
    private final Set<String> searchTerms;
    private final String userInput;

    /**
     * Construct.
     *
     * @param userInput   as given by the user
     * @param searchTerms extracted from user input
     * @param categories  extracted from user input
     */
    public UserSearchKeywords(String userInput, Set<String> searchTerms, Set<Category> categories) {
        this.categories = categories;
        this.searchTerms = searchTerms;
        this.userInput = userInput;
    }

    public final Set<String> getSearchTerms() {
        return searchTerms;
    }

    public final Set<Category> getCategories() {
        return categories;
    }

    public final String getUserInput() {
        return userInput;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public final int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result
                + ((categories == null) ? 0 : categories.hashCode());
        result = prime * result
                + ((searchTerms == null) ? 0 : searchTerms.hashCode());
        result = prime * result
                + ((userInput == null) ? 0 : userInput.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof UserSearchKeywords)) {
            return false;
        }
        UserSearchKeywords other = (UserSearchKeywords) obj;
        if (categories == null) {
            if (other.categories != null) {
                return false;
            }
        } else if (!categories.equals(other.categories)) {
            return false;
        }
        if (searchTerms == null) {
            if (other.searchTerms != null) {
                return false;
            }
        } else if (!searchTerms.equals(other.searchTerms)) {
            return false;
        }
        if (userInput == null) {
            if (other.userInput != null) {
                return false;
            }
        } else if (!userInput.equals(other.userInput)) {
            return false;
        }
        return true;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public final String toString() {
        return "UserSearchKeywords [categories=" + categories
                + ", searchTerms=" + searchTerms + ", userInput=" + userInput
                + "]";
    }
}
