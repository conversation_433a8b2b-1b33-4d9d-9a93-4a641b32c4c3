package com.gumtree.search;

import com.google.common.base.Optional;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.search.sorting.Sorting;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;

/**
 * Describes search refinements
 *
 * <AUTHOR>
 */
public class UserSearchRefinement {

    /**
     * A default instance.
     */
    public static final UserSearchRefinement EMPTY = new UserSearchRefinement.Builder().build();

    private final Collection<Attribute> attributes;
    private final Sorting sorting;
    private final boolean urgentAdsOnly;
    private final boolean adsWithImagesOnly;
    private final boolean searchInDescription;
    private final boolean featuredAdsOnly;
    private final Optional<Double> distance;
    private final Optional<Integer> maxAdAge;

    /**
     * Construct.
     */
    UserSearchRefinement(Builder builder) {
        // this is important for cases for ensuring the correct behaviour of
        // equals and hashcode
        this.attributes = new ArrayList<Attribute>(builder.attributes);
        this.sorting = builder.sorting;
        this.urgentAdsOnly = builder.urgentAdsOnly;
        this.adsWithImagesOnly = builder.adsWithImagesOnly;
        this.searchInDescription = builder.searchInDescription;
        this.featuredAdsOnly = builder.featuredAdsOnly;
        this.distance = builder.distance;
        this.maxAdAge = builder.maxAdAge;
    }

    public boolean isUrgentAdsOnly() {
        return urgentAdsOnly;
    }

    public boolean isAdsWithImagesOnly() {
        return adsWithImagesOnly;
    }

    public Collection<Attribute> getAttributes() {
        return attributes;
    }

    public boolean isFeaturedAdsOnly() {
        return featuredAdsOnly;
    }

    /**
     * @param attributeType searched
     * @return attribute of this type
     */
    public Attribute getAttribute(String attributeType) {
        for (Attribute attribute : attributes) {
            if (attribute.getType().equals(attributeType)) {
                return attribute;
            }
        }
        return null;
    }

    public Sorting getSorting() {
        return sorting;
    }

    public boolean getSearchInDescription() {
        return searchInDescription;
    }

    public boolean getFeaturedAdsOnly() {
        return featuredAdsOnly;
    }

    public Optional<Double> getDistance() {
        return distance;
    }

    public Optional<Integer> getMaxAdAge() {
        return maxAdAge;
    }

    /* (non-Javadoc)
    * @see java.lang.Object#toString()
    */
    @Override
    public String toString() {
        return "UserSearchRefinement [attributes=" + attributes + ", sorting=" + sorting + ", urgentAdsOnly="
                + urgentAdsOnly + ", adsWithImagesOnly=" + adsWithImagesOnly + ", searchInDescription="
                + searchInDescription + ", featuredAdsOnly=" + featuredAdsOnly + ", distance=" + distance
                + ", maxAdAge=" + maxAdAge + "]";
    }

    /* (non-Javadoc)
    * @see java.lang.Object#hashCode()
    */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + (adsWithImagesOnly ? 1231 : 1237);
        result = prime * result
                + ((attributes == null) ? 0 : attributes.hashCode());
        result = prime * result + (searchInDescription ? 1231 : 1237);
        result = prime * result + ((sorting == null) ? 0 : sorting.hashCode());
        result = prime * result + (urgentAdsOnly ? 1231 : 1237);
        result = prime * result + (featuredAdsOnly ? 1231 : 1237);
        result = 31 * result + (distance != null ? distance.hashCode() : 0);
        result = 31 * result + (maxAdAge != null ? maxAdAge.hashCode() : 0);
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof UserSearchRefinement)) {
            return false;
        }
        UserSearchRefinement other = (UserSearchRefinement) obj;
        if (adsWithImagesOnly != other.adsWithImagesOnly) {
            return false;
        }
        if (attributes == null) {
            if (other.attributes != null) {
                return false;
            }
        } else if (!attributes.equals(other.attributes)) {
            return false;
        }
        if (searchInDescription != other.searchInDescription) {
            return false;
        }
        if (sorting == null) {
            if (other.sorting != null) {
                return false;
            }
        } else if (!sorting.equals(other.sorting)) {
            return false;
        }
        if (urgentAdsOnly != other.urgentAdsOnly) {
            return false;
        }
        if (featuredAdsOnly != other.featuredAdsOnly) {
            return false;
        }
        if (distance != null ? !distance.equals(other.distance) : other.distance != null) {
            return false;
        }
        if (maxAdAge != null ? !maxAdAge.equals(other.maxAdAge) : other.maxAdAge != null) {
            return false;
        }

        return true;
    }

    /**
     * Builder
     *
     * <AUTHOR>
     */
    public static final class Builder {
        private Collection<Attribute> attributes = Collections.emptyList();
        private Sorting sorting = Sorting.DEFAULT;
        private boolean urgentAdsOnly = false;
        private boolean adsWithImagesOnly = false;
        private boolean searchInDescription = false;
        private boolean featuredAdsOnly = false;
        private Optional<Double> distance = Optional.absent();
        private Optional<Integer> maxAdAge = Optional.absent();

        /**
         * For default values.
         */
        public Builder() {
            // nothing to do
        }

        /**
         * based on a template
         *
         * @param toCopy not null
         */
        public Builder(UserSearchRefinement toCopy) {
            this.attributes = toCopy.attributes;
            this.sorting = toCopy.sorting;
            this.urgentAdsOnly = toCopy.urgentAdsOnly;
            this.adsWithImagesOnly = toCopy.adsWithImagesOnly;
            this.searchInDescription = toCopy.searchInDescription;
            this.featuredAdsOnly = toCopy.featuredAdsOnly;
            this.distance = toCopy.distance;
            this.maxAdAge = toCopy.maxAdAge;
        }

        /**
         * @param value to be set
         * @return same instance
         */
        public Builder sorting(Sorting value) {
            this.sorting = value;
            return this;
        }

        /**
         * @param value to be set
         * @return same instance
         */
        public Builder urgentAdsOnly(boolean value) {
            this.urgentAdsOnly = value;
            return this;
        }

        /**
         * @param value to be set
         * @return same instance
         */
        public Builder adsWithImagesOnly(boolean value) {
            this.adsWithImagesOnly = value;
            return this;
        }

        /**
         * @param value to be set
         * @return same instance
         */
        public Builder attributes(Collection<Attribute> value) {
            this.attributes = value;
            return this;
        }

        /**
         * @param value to be set
         * @return same instance
         */
        public Builder searchInDescription(boolean value) {
            this.searchInDescription = value;
            return this;
        }

        /**
         * @param value to be set
         * @return same instance
         */
        public Builder featuredAdsOnly(boolean value) {
            this.featuredAdsOnly = value;
            return this;
        }

        /**
         * @param value to be set
         * @return same instance
         */
        public Builder distance(Optional<Double> value) {
            Assert.notNull(value);
            this.distance = value;
            return this;
        }

        /**
         * @param value to be set
         * @return same instance
         */
        public Builder maxAdvertAge(Optional<Integer> value) {
            Assert.notNull(value);
            this.maxAdAge = value;
            return this;
        }

        /**
         * @return new instance
         */
        public UserSearchRefinement build() {
            return new UserSearchRefinement(this);
        }
    }

}
