package com.gumtree.thirdparty.reporting.facebook;

import java.util.HashMap;

/**
 * FaceBookTag class, used to populate and display meta tags for faceBook crawlers.
 * The tag information is displayed as meta tags by facebook.tag
 */
public abstract class FaceBookTag extends HashMap<String, String> {

    private static final String SITE_NAME = "Gumtree.com";
    private static final String COUNTRY_NAME = "UK";
    private static final String PAGE_ID = "14169285463";
    private static final String ADMINS = "712402357, 754015025, 61305173";
    private static final String FB_PAGE_ID = "fb:page_id";
    private static final String FB_ADMINS = "fb:admins";
    private static final String OG_SITE_NAME = "og:site_name";
    private static final String OG_COUNTRY_NAME = "og:country-name";
    private static final String OG_TITLE = "og:title";
    private static final String OG_TYPE = "og:type";
    private static final String OG_URL = "og:url";
    private static final String OG_DESCRIPTION = "og:description";

    protected static final String MOBILE_URL = "http://m.gumtree.com";
    protected static final String URL = "http://www.gumtree.com";

    /**
     * Empty constructor so that we can put an empty tag in for controllers that haven't yet implemented this
     */
    protected void addFaceBookTags(String pageTitle, String pageDescription) {
        this.put(FB_PAGE_ID, PAGE_ID);
        this.put(FB_ADMINS, ADMINS);
        this.put(OG_SITE_NAME, SITE_NAME);
        this.put(OG_COUNTRY_NAME, COUNTRY_NAME);
        this.put(OG_TITLE, pageTitle);
        this.put(OG_DESCRIPTION, pageDescription);
        this.put(OG_TYPE, getType());
        this.put(OG_URL, getUrl());
    }

    protected String getType() {
        return "website";
    }

    protected abstract String getUrl();

    public String getAlternateMobileUrl() {
        String url = getUrl();
        return url.replace("www.", "m.");
    }
}
