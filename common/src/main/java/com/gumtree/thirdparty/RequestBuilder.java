package com.gumtree.thirdparty;

/**
 * A request builder is responsible for building a tile, it may or may not take
 * arguments like parameters and properties
 *
 * @param <T> the component class type
 * @param <P> the parameter class type
 */
public interface RequestBuilder<T, P extends RequestParameters> {

    /**
     * Create a new component.
     *
     * @param parameters the request parameters
     * @return the component
     */
    T createComponent(P parameters);
}
