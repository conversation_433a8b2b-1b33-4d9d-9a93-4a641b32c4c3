package com.gumtree.common.model.location;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * A bean which, on creation, makes a call to retrieve the location model
 * and thus cache it on startup.
 * <p/>
 */
@Component
public class LocationModelInitializer implements InitializingBean {

    @Autowired
    private LocationModelManager locationModelManager;

    /**
     * {@inheritDoc}
     */
    @Override
    public final void afterPropertiesSet() throws Exception {
        // Simply making the call will force the model to be cached by the manager
        locationModelManager.getLocationModel();
    }
}
