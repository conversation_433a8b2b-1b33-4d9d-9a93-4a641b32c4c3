package com.gumtree.common.model.location.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.client.spec.LocationApi;
import com.gumtree.common.model.location.LocationModel;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.location.entity.LocationEntity;
import com.gumtree.service.conversion.LocationToLocationEntityConverter;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Loads location data into memory via the Bushfire API
 * <p/>
 * TODO: Further refactoring maybe required in due course so we cut down on repeated domain objects
 */
public class ApiLocationModel implements LocationModel {

    private static final String[] COUNTRY_NAMES = new String[]{"northern-ireland", "england", "scotland", "wales"};

    private Map<Integer, LocationEntity> locationByIdMap = new HashMap<Integer, LocationEntity>();

    private Map<String, LocationEntity> locationByNameMap = new HashMap<String, LocationEntity>();

    private Map<String, Collection<Location>> locationsByDisplayNameMap = new HashMap<String, Collection<Location>>();

    private Map<Integer, List<LocationEntity>> zoomInsMap = new HashMap<Integer, List<LocationEntity>>();

    private Map<Integer, List<LocationEntity>> zoomOutsMap = new HashMap<Integer, List<LocationEntity>>();

    private Map<Integer, List<LocationEntity>> nearbysMap = new HashMap<Integer, List<LocationEntity>>();

    private Map<Integer, Integer> locationLevelMap = new HashMap<Integer, Integer>();

    private Map<Integer, Location> locationToCountyMap = new HashMap<Integer, Location>();

    private Map<Integer, Map<Integer, Location>> locationHierarchyMap = Maps.newHashMap();

    private LocationEntity root;

    private List<Location> countries = new ArrayList<Location>();

    private AlphabeticalLocationComparator comparator = new AlphabeticalLocationComparator();

    private LocationToLocationEntityConverter converter = new LocationToLocationEntityConverter();

    /**
     * Constructor.
     *
     * @param locationApi         the api for loading locations and outcodes
     */
    public ApiLocationModel(LocationApi locationApi) {
        List<com.gumtree.api.Location> apiLocations = locationApi.locations().getLocations();
        for (com.gumtree.api.Location location : apiLocations) {
            LocationEntity locationEntity = converter.convert(location);
            locationByIdMap.put(locationEntity.getId(), locationEntity);
            locationByNameMap.put(locationEntity.getName(), locationEntity);

            String normalisedDisplayName = locationEntity.getDisplayName().toLowerCase();
            Collection<Location> forDisplayName = locationsByDisplayNameMap.get(normalisedDisplayName);
            if (forDisplayName == null) {
                forDisplayName = new ArrayList<Location>();
                locationsByDisplayNameMap.put(normalisedDisplayName, forDisplayName);
            }
            forDisplayName.add(locationEntity);
        }

        // Populate associations
        for (com.gumtree.api.Location location : apiLocations) {
            Integer locationId = location.getId().intValue();
            populateAssociations(locationId, location.getZoomIns(), zoomInsMap);
            populateAssociations(locationId, location.getZoomOuts(), zoomOutsMap);
            populateAssociations(locationId, location.getNearbys(), nearbysMap);
        }

        root = initialiseRootEntity();
        populateLevels(getRootLocation());

        populateCountriesList();

        for (LocationEntity location : locationByIdMap.values()) {
            locationToCountyMap.put(location.getId(), getCountyFromLocation(location));
        }
    }

    @Override
    public final Collection<Location> getAll() {
        return new ArrayList<Location>(locationByIdMap.values());
    }

    @Override
    public final Location getById(Integer id) {
        return locationByIdMap.get(id);
    }

    @Override
    public final Location getByName(String name) {
        return locationByNameMap.get(name);
    }

    @Override
    public final Collection<Location> getZoomIn(Location location) {
        List<LocationEntity> zoomIns = zoomInsMap.get(location.getId());
        return zoomIns != null ? new ArrayList<Location>(zoomIns) : new ArrayList<Location>();
    }

    @Override
    public final Collection<Location> getZoomOut(Location location) {
        List<LocationEntity> zoomOuts = zoomOutsMap.get(location.getId());
        return zoomOuts != null ? new ArrayList<Location>(zoomOuts) : new ArrayList<Location>();
    }

    @Override
    public final Collection<Location> getNearby(Location location) {
        List<LocationEntity> nearbys = nearbysMap.get(location.getId());
        return nearbys != null ? new ArrayList<Location>(nearbys) : new ArrayList<Location>();
    }

    @Override
    public final boolean hasZoomIn(Location location) {
        return zoomInsMap.containsKey(location.getId())
                && zoomInsMap.get(location.getId()) != null
                && !zoomInsMap.get(location.getId()).isEmpty();
    }

    @Override
    public final List<Location> getByNames(String... names) {
        List<Location> locations = new ArrayList<Location>();

        for (String locationName : names) {
            Location location = getByName(locationName);
            if (location != null) {
                locations.add(location);
            }
        }
        return locations;
    }

    @Override
    public final Location getPrimaryLocation(List<Integer> locationIds) {
        // The smallest location is generally the most relevant
        return getById(getSmallestLocation(locationIds));
    }

    @Override
    public final Integer getSmallestLocation(Collection<Integer> locationIds) {
        Integer currentPrimary = null;
        for (Integer id : locationIds) {
            if (currentPrimary == null) {
                currentPrimary = id;
                continue;
            }

            if (locationLevelMap.get(id) > locationLevelMap.get(currentPrimary)) {
                currentPrimary = id;
            }
        }
        return currentPrimary;
    }

    @Override
    public final Location getRootLocation() {
        return root;
    }

    @Override
    public final Location getCounty(Location location) {
        return locationToCountyMap.get(location.getId());
    }

    @Override
    public final Boolean isCounty(Location location) {
        return countries.contains(location.getId());
    }

    @Override
    public final Collection<Location> findByDisplayName(String displayName) {
        Collection<Location> locations = locationsByDisplayNameMap.get(displayName.toLowerCase());
        return locations != null ? locations : Collections.<Location>emptyList();
    }

    @Override
    public Map<Integer, Location> getLocationHierarchy(Location location) {
        Map<Integer, Location> locationHierarchy = locationHierarchyMap.get(location.getId());
        if (locationHierarchy == null) {
            locationHierarchy = buildLocationHierarchy(location);
            locationHierarchyMap.put(location.getId(), locationHierarchy);
        }
        return Maps.newLinkedHashMap(locationHierarchy);
    }

    private Map<Integer, Location> buildLocationHierarchy(Location baseLocation) {
        return buildLocationHierarchy(getLocationsList(baseLocation));
    }

    private List<Location> getLocationsList(Location baseLocation) {
        List<Location> locations = Lists.newArrayList(baseLocation);
        Collection<Location> zoomOuts = getZoomOut(baseLocation);
        while (!CollectionUtils.isEmpty(zoomOuts)) {
            Location zoomOut = zoomOuts.iterator().next();
            locations.add(zoomOut);
            zoomOuts = getZoomOut(zoomOut);
        }
        Collections.reverse(locations);
        return locations;
    }

    private Map<Integer, Location> buildLocationHierarchy(List<Location> locations) {
        Map<Integer, Location> locationHierarchy = Maps.newLinkedHashMap();
        int i = 0;
        for (Location location : locations) {
            locationHierarchy.put(++i, location);
        }
        return locationHierarchy;
    }

    /**
     * To throw when the location data is not structured properly
     */
    public final class DataStructureException extends RuntimeException {

        /**
         * Constructor.
         *
         * @param badIds the culprit location ids
         */
        public DataStructureException(List<Integer> badIds) {
            super("Data structure has bad data in it: " + badIds);
        }

        /**
         * Default constructor.
         *
         * @param cause the reason the structure is bad
         */
        public DataStructureException(String cause) {
            super("Data structure has bad data in it: " + cause);
        }
    }

    private void populateLevels(Location rootLocation) {
        Integer level = 0;
        locationLevelMap.put(rootLocation.getId(), level);
        populateLevels(rootLocation.getId(), level);
    }

    private void populateLevels(Integer id, Integer level) {
        List<LocationEntity> zoomIns = zoomInsMap.get(id);

        if (zoomIns != null && zoomIns.size() > 0) {
            level++;
            for (LocationEntity zoomIn : zoomIns) {
                locationLevelMap.put(zoomIn.getId(), level);
                populateLevels(zoomIn.getId(), level);
            }
        }
    }

    private void populateAssociations(
            Integer locationId,
            List<Long> associations,
            Map<Integer, List<LocationEntity>> associationsMap) {

        if (associations != null) {
            for (Long id : associations) {
                List<LocationEntity> associationEntities = associationsMap.get(locationId);
                if (associationEntities == null) {
                    associationEntities = new ArrayList<LocationEntity>();
                    associationsMap.put(locationId, associationEntities);
                }
                LocationEntity associationEntity = locationByIdMap.get(id.intValue());
                if (associationEntity != null) {
                    associationEntities.add(associationEntity);
                }
                Collections.sort(associationEntities, comparator);
            }
        }
    }

    private LocationEntity initialiseRootEntity() {
        List<Integer> rootIds = new ArrayList<Integer>();
        for (Integer rootId : locationByIdMap.keySet()) {
            if (!zoomOutsMap.containsKey(rootId)) {
                rootIds.add(rootId);
            }
        }

        if (rootIds.size() > 1) {
            throw new DataStructureException(rootIds);
        } else if (rootIds.isEmpty()) {
            throw new DataStructureException("no root");
        } else {
            return locationByIdMap.get(rootIds.get(0));
        }
    }

    private Location getCountyFromLocation(Location location) {
        Location county = location; //for landing pages
        List<Location> zoomOutList = new ArrayList<Location>(this.getZoomOut(location));

        if (zoomOutList.isEmpty()) { //if you are actually on the root
            county = location;
        } else {
            while (!zoomOutList.isEmpty()) {
                Location zoomedOutPlace = zoomOutList.get(0);

                if (zoomedOutPlace.equals(root) || countries.contains(zoomedOutPlace)) {
                    //county are 1 level below the root so we break here
                    break;
                } else {
                    zoomOutList = new ArrayList<Location>(this.getZoomOut(zoomedOutPlace));
                    county = zoomedOutPlace;
                }
            }
        }
        return county;
    }

    private String processDisplayNames(LocationEntity location, Map<Integer, Location> locationToCountyMap) {
        Location l1Location = locationToCountyMap.get(location.getId());
        if (!l1Location.equals(location)) {
            StringBuilder sb = new StringBuilder();
            sb.append(location.getDisplayName());
            sb.append(", ");
            sb.append(l1Location.getDisplayName());
            return sb.toString();
        }
        return location.getDisplayName();
    }

    private void populateCountriesList() {
        for (Collection<Location> locations : locationsByDisplayNameMap.values()) {
            for (Location location : locations) {
                if (isCountry(location)) {
                    countries.add(location);
                }
            }
        }
    }

    private boolean isCountry(Location location) {
        String locationName = location.getName();
        for (String countryName : COUNTRY_NAMES) {
            if (locationName.equals(countryName)) {
                return true;
            }
        }
        return false;
    }
}
