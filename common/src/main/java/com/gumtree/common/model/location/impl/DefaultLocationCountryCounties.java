package com.gumtree.common.model.location.impl;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Function;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.common.model.location.LocationCountryCounties;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.location.entity.LocationEntity;
import com.gumtree.service.location.LocationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * Default implementation of location countries
 */
@Component
public final class DefaultLocationCountryCounties implements LocationCountryCounties {

    public static final Location ENGLAND = countryLocation(99999996, "england", "England");
    public static final Location SCOTLAND = countryLocation(99999997, "scotland", "Scotland");
    public static final Location WALES = countryLocation(99999998, "wales", "Wales");
    public static final Location NORTHERN_IRELAND = countryLocation(99999999, "northernIreland", "Nothern Ireland");

    private static final Map<String, Location> COUNTRIES = ImmutableMap.copyOf(Maps.uniqueIndex(ukCountries(), toId()));

    private static Location countryLocation(Integer id, String name, String displayName) {
        LocationEntity entity = new LocationEntity();
        entity.setId(id);
        entity.setName(name);
        entity.setDisplayName(displayName);
        return entity;
    }

    private static ArrayList<Location> ukCountries() {
        return Lists.newArrayList(ENGLAND, SCOTLAND, WALES, NORTHERN_IRELAND);
    }

    private static Function<Location, String> toId() {
        return new Function<Location, String>() {
            @Override
            public String apply(@Nullable Location input) {
                return input.getId().toString();
            }
        };
    }

    private final LocationService locationService;

    private Map<Location, List<Location>> countryCountiesMap;
    private Map<Location, Location> countyCountryMap;

    @Autowired
    public DefaultLocationCountryCounties(LocationService locationService) {
        this.locationService = locationService;
    }

    @PostConstruct
    private void postConstruct() throws IOException {
        Properties countryMappings = new Properties();
        countryMappings.load(getClass().getClassLoader().getResourceAsStream("country-mappings.properties"));
        loadFromProperties(countryMappings);
    }

    @VisibleForTesting
    protected void loadFromProperties(Properties countryMappings) {
        countryCountiesMap = Maps.newHashMap();
        countyCountryMap = Maps.newHashMap();
        for (Map.Entry<Object, Object> entry : countryMappings.entrySet()) {
            Location country = COUNTRIES.get(entry.getValue());
            Location county = locationService.getById(Integer.parseInt((String) entry.getKey()));
            if (!countryCountiesMap.containsKey(country)) {
                countryCountiesMap.put(country, new ArrayList<Location>());
            }
            countyCountryMap.put(county, country);
            countryCountiesMap.get(country).add(county);
        }
    }

    @Override
    public List<Location> getCounties(Location country) {
        return countryCountiesMap.get(country);
    }

    @Override
    public Location getCountry(Location county) {
        return countyCountryMap.get(county);
    }


}
