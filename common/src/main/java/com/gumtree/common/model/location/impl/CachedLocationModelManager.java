package com.gumtree.common.model.location.impl;

import com.googlecode.ehcache.annotations.Cacheable;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.common.model.location.LocationModel;
import com.gumtree.common.model.location.LocationModelManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Manages and caches an in memory location model - built via the Bushfire API.
 */
@Component
public class CachedLocationModelManager implements LocationModelManager {

    @Autowired
    private BushfireApi bushfireApi;

    @Cacheable(cacheName = "locationModel", selfPopulating = true)
    @Override
    public final LocationModel getLocationModel() {
        return new ApiLocationModel(bushfireApi.locationApi());
    }
}
