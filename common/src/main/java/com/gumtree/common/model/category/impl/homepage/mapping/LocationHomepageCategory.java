package com.gumtree.common.model.category.impl.homepage.mapping;

import org.codehaus.jackson.annotate.JsonProperty;

/**
 * Location Homepage Category Model Object (for mapping from JSON config file).
 */
public final class LocationHomepageCategory {

    private String name;

    @JsonProperty(value = "abundance_visible")
    private Boolean abundanceVisible;

    @JsonProperty(value = "sort_group_index")
    private Integer sortGroupIndex;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean isAbundanceVisible() {
        return abundanceVisible;
    }

    public void setAbundanceVisible(Boolean abundanceVisible) {
        this.abundanceVisible = abundanceVisible;
    }

    public Integer getSortGroupIndex() {
        return sortGroupIndex;
    }

    public void setSortGroupIndex(Integer sortGroupIndex) {
        this.sortGroupIndex = sortGroupIndex;
    }
}
