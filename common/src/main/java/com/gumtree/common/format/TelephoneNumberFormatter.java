package com.gumtree.common.format;

public final class TelephoneNumberFormatter {

    private TelephoneNumberFormatter(){

    }

    public static final String MASK = "XXXX";

    /**
     * @param number The number to mask
     * @return The masked number
     */
    public static String mask(String number) {

        StringBuilder sb = new StringBuilder();
        if(number.length() > MASK.length()) {
            String visiblePortion = number.substring(0, number.length() - MASK.length());
            sb.append(visiblePortion);
        }
        sb.append(MASK);
        return sb.toString();
    }

}
