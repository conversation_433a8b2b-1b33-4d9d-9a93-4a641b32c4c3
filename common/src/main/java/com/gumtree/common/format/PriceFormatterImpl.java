package com.gumtree.common.format;

import com.google.common.base.Optional;
import com.google.common.primitives.Longs;
import com.gumtree.api.PriceFrequency;
import com.gumtree.domain.advert.Price;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.AttributeService;
import com.gumtree.domain.newattribute.DisplayAttribute;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.Locale;

import static com.gumtree.common.util.time.ConversionConstants.WEEKS_IN_A_MONTH;


/**
 * Helper class for price formatting.
 *
 * <AUTHOR> / mkelly
 */
@Component
public final class PriceFormatterImpl implements PriceFormatter {

    public static final String POUND = "\u00A3";

    @Autowired
    private AttributeService attributeService;

    /**
     * Setter for the attribute service
     *
     * @param attributeService the service
     */
    public void setAttributeService(AttributeService attributeService) {
        this.attributeService = attributeService;
    }

    /**
     * Format a price.
     *
     *
     * @param price                   of the advert
     * @param priceFrequencyAttribute of the advert
     * @param categoryId              not null categoryId
     * @return the formatted price
     * @throws IllegalArgumentException if the categoryId is NULL
     */
    @Override
    public String format(Price price, Attribute priceFrequencyAttribute, Long categoryId) {
        BigDecimal priceAmount = price != null ? price.getAmount() : null;
        return format(priceAmount, priceFrequencyAttribute, categoryId);
    }

    /**
     * Format a price.
     *
     *
     * @param price                   of the advert
     * @param priceFrequencyAttribute of the advert
     * @param categoryId              not null categoryId
     * @return the formatted price
     * @throws IllegalArgumentException if the categoryId is NULL
     */
    @Override
    public String format(BigDecimal price, Attribute priceFrequencyAttribute, Long categoryId) {
        Assert.notNull(categoryId);

        if (price != null) {
            String displayPrice = normalisePrice(price.doubleValue());
            displayPrice += getPriceFrequency(priceFrequencyAttribute, categoryId);
            return displayPrice;
        }

        return "";
    }

    public String formatConvertingMonthlyToWeekly(
            BigDecimal price, Attribute priceFrequencyAttribute, Long categoryId) {
        Assert.notNull(categoryId);
        if (price != null) {
            double priceDouble = price.doubleValue();
            String priceFrequency = getPriceFrequency(priceFrequencyAttribute, categoryId);
            double normalizedPrice = convertMontlhyPriceToWeeklyPrice(priceDouble, priceFrequency);
            String displayPrice = normalisePrice(normalizedPrice);
            displayPrice += priceFrequency.length() > 0 ? "pw" : "";
            return displayPrice;
        }
        return "";
    }

    private double convertMontlhyPriceToWeeklyPrice(double priceDouble, String priceFrequency) {
        if (priceFrequency.equals("pm")) {              //per_month
            return (long) Math.ceil(priceDouble / WEEKS_IN_A_MONTH);
        }
        return priceDouble;
    }

    private String getPriceFrequency(Attribute priceFrequencyAttribute, Long categoryId) {
        String displayPrice;
        if (priceFrequencyAttribute != null) {
            Optional<DisplayAttribute> displayAttribute = attributeService.getDisplayAttribute(
                    priceFrequencyAttribute, categoryId);
            displayPrice = displayAttribute.isPresent() ? displayAttribute.get().getDisplayValue() : "";
        } else {
            displayPrice = "";
        }
        return displayPrice;
    }

    /**
     * @param price          of advert
     * @param priceFrequency freq
     * @return the normalised price value
     */
    @Override
    public String format(com.gumtree.api.Price price, PriceFrequency priceFrequency) {

        if (price != null) {
            String displayPrice = normalisePrice(price.getAmount().doubleValue() / 100);

            if (priceFrequency != null) {
                displayPrice += priceFrequency.getName();
            }

            return displayPrice;
        }

        return "";
    }

    public static String formatWithZeros(Double price) {
        NumberFormat numberFormatter = NumberFormat.getInstance(Locale.ENGLISH);
        numberFormatter.setMaximumFractionDigits(2);
        if (!price.equals(Math.floor(price))) {
            numberFormatter.setMinimumFractionDigits(2);
        }
        return (price != null) ? POUND + numberFormatter.format(price) : "";
    }

    public String normalisePrice(Double price) {
        NumberFormat numberFormatter = NumberFormat.getInstance(Locale.ENGLISH);
        numberFormatter.setMaximumFractionDigits(2);
        if (!price.equals(Math.floor(price))) {
            numberFormatter.setMinimumFractionDigits(2);
        }
        return (price != null && price > 0) ? POUND + numberFormatter.format(price) : "";
    }

    @Override
    public String formatSmall(com.gumtree.api.Price price, PriceFrequency priceFrequency) {
        if (price != null) {
            String displayPrice = normalisePrice(price.getAmount().doubleValue() / 100);

            if (priceFrequency != null) {
                displayPrice += normalisePriceFrequency(priceFrequency.getCode());
            }

            return displayPrice;
        }

        return "";
    }

    public static String formatPriceInPence(String value) {
        NumberFormat numberFormatter = NumberFormat.getInstance(Locale.ENGLISH);
        numberFormatter.setMaximumFractionDigits(2);

        Long priceInPence = Longs.tryParse(value);
        Double price = priceInPence.doubleValue() / 100;
        if (!price.equals(Math.floor(price))) {
            numberFormatter.setMinimumFractionDigits(2);
        }
        return (price != null) ? numberFormatter.format(price) : "";
    }

    private String normalisePriceFrequency(String code) {
        if (code.equals(PriceFrequency.MONTHLY.getCode())) {
            return "pm";
        }
        return "pw";
    }

}


