package com.gumtree.service.category.impl;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.display.AttributeDisplayMetadata;
import com.gumtree.service.category.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Implementation of {@link CategoryService}
 *
 * <AUTHOR>
 */
@Service
public final class CategoryServiceImpl implements CategoryService {
    protected static final Set<String> HIDDEN_CATEGORIES = Sets.newHashSet("competitions");
    private final CategoryModel categoryModel;

    @Autowired
    public CategoryServiceImpl(CategoryModel categoryModel) {
        this.categoryModel = categoryModel;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean exists(String name) {
        return categoryModel.getByName(name).isPresent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean exists(Long id) {
        return categoryModel.getCategory(id).isPresent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Optional<Category> getByUniqueName(String name) {
        return categoryModel.getByName(name);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Optional<Category> getById(Long id) {
        return categoryModel.getCategory(id);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Optional<Category> getParent(Category category) {
        return categoryModel.getParentOf(category.getId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Optional<Category> getL1Category(Long id) {
        return categoryModel.getL1CategoryFor(id);
    }

    /**
     * Retrieves the list of child categories for the given category
     *
     * @param category category to retreive child categories for
     * @return category list of children
     */
    @Override
    public List<Category> getChildCategories(Category category) {
        return category.getChildren();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isLeaf(Category category) {
        return getChildCategories(category).isEmpty();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Collection<Category> findByDisplayName(String part) {
        return categoryModel.findByDisplayName(part);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isChild(Category potentialChild, Category category) {
        return categoryModel.isChild(category.getId(), potentialChild.getId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer getCategoryLevel(Long id) {
        return categoryModel.getHierarchy(id).size();
    }

    /**
     * {@inheritDoc}
     * <p/>
     * TODO: Unit test
     */
    @Override
    public Map<Integer, Category> getLevelHierarchy(Category category) {
        if (category == null) {
            return Maps.newHashMap();
        }
        return getLevelHierarchy(category.getId());
    }

    @Override
    public Map<Integer, Category> getLevelHierarchy(Long categoryId) {
        List<Category> hierarchyList = categoryModel.getFullPath(categoryId);
        Map<Integer, Category> hierarchyMap = Maps.newHashMap();
        Integer level = 0;
        for (Category listCategory : hierarchyList) {
            hierarchyMap.put(level, listCategory);
            level++;
        }
        return hierarchyMap;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Collection<Category> getAll() {
        return categoryModel.getAllCategories();
    }

    @Override
    public List<Category> getEnabledAndNotVirtual() {
        return categoryModel.getAllCategories();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isHidden(Category category) {
        return HIDDEN_CATEGORIES.contains(category.getSeoName());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<String> attributeTypes(Long categoryId) {
        Optional<Category> category = categoryModel.getCategory(categoryId);
        return category.isPresent()
                ? attributeTypesList(category.get())
                : new ArrayList<String>();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Optional<String> attributeDisplayLabel(Long categoryId, String attributeName) {
        Optional<Category> categoryOption = categoryModel.getCategory(categoryId);
        return categoryOption.isPresent()
                ? attributeDisplayLabel(categoryOption.get(), attributeName)
                : Optional.<String>absent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Optional<String> attributeValueDisplayLabel(Long categoryId, String attributeName, String value) {
        Optional<Category> categoryOption = categoryModel.getCategory(categoryId);
        return categoryOption.isPresent()
                ? attributeValueDisplayLabel(categoryOption.get(), attributeName, value)
                : Optional.<String>absent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean supportsAttribute(Long categoryId, String attributeName) {
        return attributeTypes(categoryId).contains(attributeName);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<AttributeMetadata> attributesByName(String name) {
        return categoryModel.findAttributesByName(name);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<Category> getCategoriesList(Category category) {
        List<Category> categories = new ArrayList<Category>();
        Optional<Category> parentCategory = Optional.of(category);
        while (parentCategory.isPresent()) {
            categories.add(parentCategory.get());
            parentCategory = getParent(parentCategory.get());
        }

        Collections.reverse(categories);

        return categories;
    }

    @Override
    public List<Category> getBranch(Category category, boolean includeDirectChildren, boolean includeRoot) {
        List<Category> categories = new ArrayList<Category>();
        if (includeDirectChildren) {
            categories.addAll(getChildCategories(category));
        }
        Optional<Category> parentCategory = Optional.of(category);
        while (parentCategory.isPresent()) {
            if (parentCategory.get().getParentId() != null
                    || category.getParentId() == null
                    || (parentCategory.get().getParentId() == null && includeRoot)) {
                categories.add(0, parentCategory.get());
            }
            parentCategory = getParent(parentCategory.get());
        }
        return categories;
    }

    @Override
    public Category getRootCategory() {
        return categoryModel.getRootCategory();
    }

    @Override
    public Optional<Collection<AttributeMetadata>> getCategoryAttributes() {
        return categoryModel.getCategoryAttribute();
    }

    @Override
    public CategoryModel getCategoryModel() {
        return categoryModel;
    }

    private List<String> attributeTypesList(Category category) {
        List<AttributeMetadata> attributes = category.getAttributeMetadata();
        if (attributes == null || attributes.isEmpty()) {
            return Lists.newArrayList();
        } else {
            List<String> attributeNames = new ArrayList<String>(attributes.size());
            for (AttributeMetadata attribute : attributes) {
                attributeNames.add(attribute.getName());
            }
            return attributeNames;
        }
    }

    private Optional<String> attributeDisplayLabel(Category category, String attributeName) {
        Optional<AttributeMetadata> attribute = category.findAttribute(attributeName.toLowerCase());
        return attribute.isPresent()
                ? Optional.fromNullable(attribute.get().getLabel())
                : Optional.<String>absent();
    }

    private Optional<String> attributeValueDisplayLabel(Category category, String attributeName, String value) {
        Optional<AttributeMetadata> attribute = category.findAttribute(attributeName);
        if (attribute.isPresent()) {
            Optional<AttributeDisplayMetadata.MatchResult> attributeValue = attribute.get().getDisplay().findValue(value);
            return attributeValue.transform(AttributeDisplayMetadata.MatchResult::getLabel);
        }
        return Optional.absent();
    }
}
