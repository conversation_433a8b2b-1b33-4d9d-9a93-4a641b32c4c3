package com.gumtree.service.location.impl;

import com.googlecode.ehcache.annotations.Cacheable;
import com.gumtree.domain.location.Location;
import com.gumtree.common.model.location.LocationModelManager;
import com.gumtree.common.model.location.impl.LocationTreeModel;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.model.tree.TreeModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Implementation of {@link LocationService}
 */
@Service
public final class LocationServiceImpl implements LocationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LocationServiceImpl.class);

    private final LocationModelManager locationModelManager;

    @Autowired
    public LocationServiceImpl(LocationModelManager locationModelManager) {
        this.locationModelManager = locationModelManager;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean exists(String name) {
        return locationModelManager.getLocationModel().getByName(name) != null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Location getByName(String name) {
        return locationModelManager.getLocationModel().getByName(name);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Location getById(Integer id) {
        return locationModelManager.getLocationModel().getById(id);
    }

    /**
     * get the primary Location from a list of location ids
     *
     * @param locationIds   list
     * @return Location, the primary location ex: site
     */
    @Override
    public Location getPrimaryLocation(List<Integer> locationIds) {
        return locationModelManager.getLocationModel().getPrimaryLocation(locationIds);
    }

    @Override
    public Integer getSmallestLocation(Collection<Integer> locationIds) {
        return locationModelManager.getLocationModel().getSmallestLocation(locationIds);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<Location> getByNames(String... names) {
        return locationModelManager.getLocationModel().getByNames(names);
    }

    @Override
    public Collection<Location> findByDisplayName(String displayName) {
        return locationModelManager.getLocationModel().findByDisplayName(displayName);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Location getLanding(String name) {

        Location location = locationModelManager.getLocationModel().getByName(name);

        if (location != null && location.isLanding()) {
            return location;
        }

        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isLanding(String name) {
        return getLanding(name) != null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Location getLanding(Location location) {
        Location landing = location;
        while (landing != null && !landing.isLanding()) {
            landing = getParent(landing);
        }

        return landing;
    }

    private Location getParent(Location location) {
        Collection<Location> zoomouts = getZoomOut(location);
        if (zoomouts.isEmpty()) {
            return null;
        }
        return zoomouts.iterator().next();
    }

    /**
     * {@inheritDoc}
     */
    @Cacheable(cacheName = "locationTrees")
    @Override
    public TreeModel<Location> getLocationTree(Location currentLocation) {
        LOGGER.info("Creating location tree for " + currentLocation.getName());
        return LocationTreeModel.buildModel(locationModelManager.getLocationModel(), currentLocation);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Collection<Location> getZoomIn(Location location) {
        return locationModelManager.getLocationModel().getZoomIn(location);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Collection<Location> getZoomOut(Location location) {
        return locationModelManager.getLocationModel().getZoomOut(location);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Collection<Location> getNearby(Location location) {
        return locationModelManager.getLocationModel().getNearby(location);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean
    hasZoomIn(Location location) {
        return locationModelManager.getLocationModel().hasZoomIn(location);
    }

    /**
     * @param location the current location where the method is called
     * @return A Location that is the current county
     */
    @Override
    // TODO: we could probably build this as a map in the InMemoryLocationModel similar to what
    // we do for l1Categories in the InMemoryCategoryModel
    public Location getCounty(Location location) {
        return locationModelManager.getLocationModel().getCounty(location);
    }

    @Override
    public Boolean isCounty(Location location) {
        return locationModelManager.getLocationModel().isCounty(location);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Location getRootLocation() {
        return locationModelManager.getLocationModel().getRootLocation();
    }

    @Override
    public Collection<Location> getAll() {
        return locationModelManager.getLocationModel().getAll();
    }

    @Override
    public Map<Integer, Location> getLocationHierarchy(Location location) {
        return locationModelManager.getLocationModel().getLocationHierarchy(location);
    }
}
