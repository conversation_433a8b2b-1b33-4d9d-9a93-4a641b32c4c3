package com.gumtree.service.jobs;

import java.io.IOException;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;

public class CvData implements Serializable {

    private Long userId;
    private Instant uploadDateTime;
    private byte[] bytes;
    private String contentType;
    private String originalFilename;

    public Long getUserId() {
        return userId;
    }

    public Instant getUploadDateTime() {
        return uploadDateTime;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public String getContentType() {
        return contentType;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public String getFilePrefix() throws IOException {
        return com.google.common.io.Files.getNameWithoutExtension(originalFilename);
    }

    public String getFileSuffix() throws IOException {
        return com.google.common.io.Files.getFileExtension(originalFilename);
    }

    public String getPeriodSinceUploaded() {
        LocalDate now = LocalDateTime.now().toLocalDate();
        LocalDate uploadLocalDateTime = LocalDateTime.from(uploadDateTime.atZone(ZoneId.systemDefault())).toLocalDate();

        long years = ChronoUnit.YEARS.between(uploadLocalDateTime, now);
        if (years == 1) {
            return "1 year ago";
        } else if (years > 0){
            return years + " years ago";
        }

        long months = ChronoUnit.MONTHS.between(uploadLocalDateTime, now);
        if (months == 1) {
            return "1 month ago";
        } else if(months > 0) {
            return months + " months ago";
        }

        long days = ChronoUnit.DAYS.between(uploadLocalDateTime, now);
        if (days == 1) {
            return "1 day ago";
        } else if (days > 0) {
            return days + " days ago";
        } else {
            return "today";
        }
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private CvData cvData;

        public Builder() {
            cvData = new CvData();
            cvData.uploadDateTime = Instant.now();
        }

        public Builder(CvData oCvData) {
            cvData = new CvData();
            withBytes(oCvData.getBytes())
                    .withUploadDate(oCvData.getUploadDateTime())
                    .withOriginalFilename(oCvData.getOriginalFilename())
                    .withUserId(oCvData.getUserId())
                    .withContentType(oCvData.contentType);
        }

        public Builder withUserId(Long userId) {
            this.cvData.userId = userId;
            return this;
        }

        public Builder withContentType(String contentType) {
            this.cvData.contentType = contentType;
            return this;
        }

        public Builder withUploadDate(Instant uploadDate) {
            this.cvData.uploadDateTime = uploadDate;
            return this;
        }


        public Builder withBytes(byte[] bytes) {
            this.cvData.bytes = bytes;
            return this;
        }

        public Builder withOriginalFilename(String originalFilename) {
            this.cvData.originalFilename = originalFilename;
            return this;
        }

        public CvData build() {
            return this.cvData;
        }

    }
}
