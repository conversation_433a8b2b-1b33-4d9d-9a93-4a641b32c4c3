package com.gumtree.service.conversion;

import com.gumtree.api.Location;
import com.gumtree.common.util.converter.AbstractConverter;
import com.gumtree.domain.location.entity.LocationEntity;
import org.springframework.stereotype.Component;

/**
 */
@Component
public class LocationToLocationEntityConverter extends AbstractConverter<Location, LocationEntity> {

    @Override
    public LocationEntity convert(Location location) {
        LocationEntity locationEntity = new LocationEntity();
        locationEntity.setId(location.getId().intValue());
        locationEntity.setLanding(location.isLanding() != null ? location.isLanding() : false);
        locationEntity.setDisplayName(location.getName());
        locationEntity.setName(location.getSeoName());
        locationEntity.setLatitude(location.getLatitude());
        locationEntity.setLongitude(location.getLongitude());
        locationEntity.setRadius(location.getRadius());
        return locationEntity;
    }
}
