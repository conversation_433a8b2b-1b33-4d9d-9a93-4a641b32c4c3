package com.gumtree.google;

import com.gumtree.domain.location.LocationCentroid;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Test the functionality of the mostly static GoogleMap class.
 */
public class GoogleMapTest {

    private GoogleMapsUrlSigner mockGoogleMapsUrlSigner;

    @Before
    public void setup() {
        mockGoogleMapsUrlSigner = mock(GoogleMapsUrlSigner.class);
    }

    /**
     * Create a default tile and match the generated url.
     */
    @Test
    public void testGetUrlDefault() throws Exception {
        when(mockGoogleMapsUrlSigner.signRequest("/maps/api/staticmap",
                "zoom=15&size=320x240&maptype=roadmap&center=1121311.0%2C1121311.0&sensor=false" +
                "&client=gme-marktplaats")).thenReturn("/signedurl");
        GoogleMap googleMap = new GoogleMap(mockGoogleMapsUrlSigner, createLocationCentroid());
        assertThat(googleMap.getUrl(), equalTo("http://maps.googleapis.com/signedurl"));
    }


    /**
     * Test assert not null on Location Centroid.
     */
    @Test
    public void testGetUrlNullLocationCentroid() {
        try {
            new GoogleMap(mockGoogleMapsUrlSigner, null);
        } catch (IllegalArgumentException ex) {
            assertNotNull(ex.getMessage());
        }
    }

    /**
     * Test assert not null on lat.
     */
    @Test
    public void testGetUrlNullLatLocationCentroid() {
        try {
            new GoogleMap(mockGoogleMapsUrlSigner, createNullLatLocationCentroid());
        } catch (IllegalArgumentException ex) {
            assertNotNull(ex.getMessage());
        }
    }

    /**
     * Test assert not null on long.
     */
    @Test
    public void testGetUrlNullLongLocationCentroid() {
        try {
            new GoogleMap(mockGoogleMapsUrlSigner, createNullLongLocationCentroid());
        } catch (IllegalArgumentException ex) {
            assertNotNull(ex.getMessage());
        }
    }

    private LocationCentroid createLocationCentroid() {
        LocationCentroid lc = mock(LocationCentroid.class);
        when(lc.getLatitude()).thenReturn(Double.valueOf(1121311));
        when(lc.getLongitude()).thenReturn(Double.valueOf(1121311));
        return lc;
    }

    private LocationCentroid createNullLatLocationCentroid() {
        LocationCentroid lc = mock(LocationCentroid.class);
        when(lc.getLatitude()).thenReturn(null);
        when(lc.getLongitude()).thenReturn(Double.valueOf(1121311));
        return lc;
    }

    private LocationCentroid createNullLongLocationCentroid() {
        LocationCentroid lc = mock(LocationCentroid.class);
        when(lc.getLatitude()).thenReturn(Double.valueOf(1121311));
        when(lc.getLongitude()).thenReturn(null);
        return lc;
    }
}
