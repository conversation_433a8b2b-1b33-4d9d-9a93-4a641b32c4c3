package com.gumtree.util.url;

import com.gumtree.domain.media.ImageSize;
import com.gumtree.web.service.images.secure.SecureImageService;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class CdnImageUrlProviderTest {

    private SecureImageService secureImageService;

    @Autowired
    private CdnImageUrlProviderImpl cdnImageUrlProvider;

    @Before
    public void before() {
        secureImageService = mock(SecureImageService.class);
        cdnImageUrlProvider = new CdnImageUrlProviderImpl(secureImageService);
    }

    @Test
    public void getImageUrlShouldReturnCloudFlareUrl() {
        //given
        String url = "https://imagedelivery.net/ePR8PyKf84wPHx7_RYmEag/132ddef3-af52-427e-9074-ba44a1731d00/1";

        //when
        String result = cdnImageUrlProvider.getImageUrl(url, ImageSize.MAIN);

        // then
        assertThat(result, equalTo("https://imagedelivery.net/ePR8PyKf84wPHx7_RYmEag/132ddef3-af52-427e-9074-ba44a1731d00/79"));
    }

    @Test
    public void getImageUrlShouldReturnEpsUrl() {
        //given
        String url = "http://i.ebayimg.com/image/path.jpg_79.jpg";

        // when
        String result = cdnImageUrlProvider.getImageUrl(url, ImageSize.MAIN);

        // then
        assertThat(url, equalTo(result));
    }

    @Test
    public void getSecureImageUrlShouldReturnCloudFlareUrl() {
        //given
        String url = "https://imagedelivery.net/ePR8PyKf84wPHx7_RYmEag/132ddef3-af52-427e-9074-ba44a1731d00/1";

        //when
        String result = cdnImageUrlProvider.getSecureImageUrl(url, ImageSize.MAIN.getId());

        // then
        assertThat(result, equalTo("https://imagedelivery.net/ePR8PyKf84wPHx7_RYmEag/132ddef3-af52-427e-9074-ba44a1731d00/79"));
    }

    @Test
    public void getSecureImageUrlShouldReturnEpsUrl() {
        //given
        String url = "http://i.ebayimg.com/image/path.jpg_79.jpg";
        String secureUrl = "https://ssli.ebayimg.com/image/path.jpg";
        when(secureImageService.getSecureUrl(any())).thenReturn(secureUrl);

        // when
        String result = cdnImageUrlProvider.getSecureImageUrl(url, ImageSize.MAIN.getId());

        // then
        assertThat(secureUrl, equalTo(result));
    }
}
