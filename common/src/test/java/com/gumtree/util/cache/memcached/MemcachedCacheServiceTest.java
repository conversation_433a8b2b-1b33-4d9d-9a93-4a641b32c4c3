package com.gumtree.util.cache.memcached;

import net.spy.memcached.CASResponse;
import net.spy.memcached.CASValue;
import net.spy.memcached.MemcachedClientIF;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import com.gumtree.util.cache.CacheNamespace;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.nullValue;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.any;

/**
 * User: salamandr
 * Date: 23/04/2011
 * Time: 11:12
 */
public class MemcachedCacheServiceTest {

    MemcachedCacheService<String> service;
    StringCacheValueFactory valueFactory;
    MemcachedClientIF client;

    @Before
    public void setUp() {
        service = new MemcachedCacheService<String>();
        client = mock(MemcachedClientIF.class);
        valueFactory = mock(StringCacheValueFactory.class);
        service.setValueFactory(valueFactory);
        service.setMemcachedClient(client);
        service.setCacheExpiry(120);
        service.setRefreshExpiry(60);
        service.setUpdaterExpiry(10);
        service.setProjectVersion("DEV");
    }

    @Test
    public void nullGetsCausesNewValue() {
        when(client.gets("test-key")).thenReturn(null);

        service.get(CacheNamespace.CATEGORY_LANDING_PAGE_MODEL, "test-key");

        verify(valueFactory).create("test-key");
    }

    @Test
    public void finishCallsShutdownOnClient() {
        service.finish();

        verify(client).shutdown(10, TimeUnit.SECONDS);
    }

    @Test
    public void getReturnsValueFromCreateOnNull() {
        when(client.gets("test-key")).thenReturn(null);
        when(valueFactory.create("test-key")).thenReturn("a-bicycle-test-with-haloita-balloons");

        assertThat(service.get(CacheNamespace.CATEGORY_LANDING_PAGE_MODEL, "test-key"), equalTo("a-bicycle-test-with-haloita-balloons"));
    }

    @Test
    public void getReturnsOldValueWhenFresh() {
        String baseKey = "a-test";
        MemcachedEntry<String> entry = getEntry(baseKey);
        entry.setValue("this-little-string-will-reveal-all");
        entry.setUpdated(new Date()); // Set to current time, i.e. fresh

        String key = CacheNamespace.CATEGORY_LANDING_PAGE_MODEL.getPrefix() + ':' + "DEV" + ':' + baseKey;
        when(client.gets(key)).thenReturn(new CASValue<Object>(1234L, entry));

        assertThat(service.get(CacheNamespace.CATEGORY_LANDING_PAGE_MODEL, baseKey), equalTo("this-little-string-will-reveal-all"));
    }

    @Test
    public void getUpdatesWhenNeedsRefresh() {
        Date now = new Date();
        String baseKey = "a-test";
        MemcachedEntry<String> entry = getEntry(baseKey);
        entry.setValue("this-value-needs-freshening-up");
        entry.setUpdated(new Date(now.getTime() - 65000)); // Set to 65 seconds before current time, i.e. needs refresh

        String key = CacheNamespace.CATEGORY_LANDING_PAGE_MODEL.getPrefix() + ':' + "DEV" + ':' + baseKey;
        when(client.gets(key)).thenReturn(new CASValue<Object>(1234L, entry));

        when(client.cas(key, 1234L, entry)).thenReturn(CASResponse.OK);

        when(valueFactory.create(baseKey)).thenReturn("this-little-string-will-reveal-all");

        assertThat(service.get(CacheNamespace.CATEGORY_LANDING_PAGE_MODEL, baseKey), equalTo("this-little-string-will-reveal-all"));

        ArgumentCaptor<MemcachedEntry> argument = ArgumentCaptor.forClass(MemcachedEntry.class);
        verify(client).set(eq(key), eq(120), argument.capture());

        assertThat((String) argument.getValue().getValue(), equalTo("this-little-string-will-reveal-all"));
    }

    @Test
    public void getUsesOldValueWhenCannotBecomeUpdater() {
        Date now = new Date();
        String baseKey = "a-test";
        MemcachedEntry<String> entry = getEntry(baseKey);
        entry.setValue("this-value-needs-freshening-up");
        entry.setUpdated(new Date(now.getTime() - 65000)); // Set to 65 seconds before current time, i.e. needs refresh

        String key = CacheNamespace.CATEGORY_LANDING_PAGE_MODEL.getPrefix() + ':' + "DEV" + ':' + baseKey;
        when(client.gets(key)).thenReturn(new CASValue<Object>(1234L, entry));

        when(client.cas(key, 1234L, entry)).thenReturn(CASResponse.EXISTS);

        when(valueFactory.create(baseKey)).thenReturn("this-little-string-will-reveal-all");

        assertThat(service.get(CacheNamespace.CATEGORY_LANDING_PAGE_MODEL, baseKey), equalTo("this-value-needs-freshening-up"));

        verify(client).gets(key);
        verify(client).cas(key, 1234L, entry);
        verifyNoMoreInteractions(client);
    }

    @Test
    public void getSetsNewValueWhenFailsToBecomeUpdater() {
        Date now = new Date();
        String baseKey = "a-test";
        MemcachedEntry<String> entry = getEntry(baseKey);
        entry.setValue("this-value-needs-freshening-up");
        entry.setUpdated(new Date(now.getTime() - 65000)); // Set to 65 seconds before current time, i.e. needs refresh

        String key = CacheNamespace.CATEGORY_LANDING_PAGE_MODEL.getPrefix() + ':' + "DEV" + ':' + baseKey;
        when(client.gets(key)).thenReturn(new CASValue<Object>(1234L, entry));

        when(client.cas(key, 1234L, entry)).thenReturn(CASResponse.NOT_FOUND);

        when(valueFactory.create(baseKey)).thenReturn("this-little-string-will-reveal-all");

        assertThat(service.get(CacheNamespace.CATEGORY_LANDING_PAGE_MODEL, baseKey), equalTo("this-little-string-will-reveal-all"));

        ArgumentCaptor<MemcachedEntry> argument = ArgumentCaptor.forClass(MemcachedEntry.class);
        verify(client).set(eq(key), eq(120), argument.capture());

        assertThat((String) argument.getValue().getValue(), equalTo("this-little-string-will-reveal-all"));
    }

    @Test
    public void requestDoesNotFailWhenMemchachedClientThrowsException() {
        when(client.gets(any(String.class))).thenThrow(new RuntimeException());
        String result = service.get(CacheNamespace.CATEGORY_LANDING_PAGE_MODEL, "my key");
        assertThat(result, nullValue());
    }

    public MemcachedEntry<String> getEntry(String key) {
        return getEntry(key, "a-placeholder-test-value");
    }

    public MemcachedEntry<String> getEntry(String key, String value) {
        MemcachedEntry<String> entry = new MemcachedEntry<String>(key);
        entry.setValue(value);
        entry.setUpdated(new Date()); // Default to current time, i.e. fresh
        return entry;
    }

}
