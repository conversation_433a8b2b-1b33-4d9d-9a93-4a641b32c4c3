package com.gumtree.util.cache.memcached;

import com.gumtree.util.cache.CacheService;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

/**
 * User: salamandr
 * Date: 23/04/2011
 * Time: 10:10
 */
public class MemcachedCacheServiceFactoryTest {

    @Test
    public void allConfigSetCorrectly() {
        StringCacheValueFactory valueFactory = new StringCacheValueFactory();
        MemcachedCacheServiceFactory factory = new MemcachedCacheServiceFactory();

        ReflectionTestUtils.setField(factory, "cacheExpiry", 1234);
        ReflectionTestUtils.setField(factory, "refreshExpiry", 2567);
        ReflectionTestUtils.setField(factory, "updaterExpiry", 3890);

        CacheService<String> service = factory.getService(valueFactory);

        assertThat((Integer) ReflectionTestUtils.getField(service, "cacheExpiry"), equalTo(1234));
        assertThat((Integer) ReflectionTestUtils.getField(service, "refreshExpiry"), equalTo(2567));
        assertThat((Integer) ReflectionTestUtils.getField(service, "updaterExpiry"), equalTo(3890));
    }

}
