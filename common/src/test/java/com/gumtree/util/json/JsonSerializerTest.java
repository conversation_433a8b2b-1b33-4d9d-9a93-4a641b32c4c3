package com.gumtree.util.json;

import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

public class JsonSerializerTest {

    @Test
    public void serializerShouldProvideTheExpectedJsonString() {

        SimpleBeanToSerialize simpleBeanToSerialize = new SimpleBeanToSerialize("TEST SIMPLE BEAN", 666);
        String jsonString = JsonSerializer.toJsonString(simpleBeanToSerialize);

        assertThat(jsonString, equalTo("{\"testString\":\"TEST SIMPLE BEAN\",\"testLong\":666}"));
    }

    @Test
    public void serializerShouldProvideTheExpectedObject() {

        SimpleBeanToSerialize deserializedBean = JsonSerializer.toObject("{\"testString\":\"TEST SIMPLE BEAN\",\"testLong\":666}", SimpleBeanToSerialize.class);

        assertThat(deserializedBean.testString, equalTo("TEST SIMPLE BEAN"));
        assertThat(deserializedBean.testLong, is(666L));
    }


    private static class SimpleBeanToSerialize {

        private SimpleBeanToSerialize() {
        }

        private SimpleBeanToSerialize(String testString, long testLong) {
            this.testString = testString;
            this.testLong = testLong;
        }

        private String testString;
        private long testLong;
    }

}
