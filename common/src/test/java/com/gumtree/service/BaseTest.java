package com.gumtree.service;

import com.netflix.config.ConfigurationManager;
import org.junit.runner.RunWith;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Properties;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(
        initializers = BaseTest.TestContextInitializer.class,
        locations = "/com/gumtree/test-context.xml")
public abstract class BaseTest {

    public static class TestContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {
        @Override
        public final void initialize(ConfigurableApplicationContext appContext) {
            Properties properties = new Properties();
            properties.setProperty("gumtree.url.buyer.base_uri", "");
            properties.setProperty("gumtree.url.seller.base_uri", "");
            properties.setProperty("gumtree.url.seller.secure.base_uri", "");
            properties.setProperty("gumtree.url.reply.base_uri", "");
            ConfigurationManager.loadProperties(properties);
        }
    }
}
