package com.gumtree.service.category.impl;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import java.util.List;
import java.util.Map;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;
import static org.springframework.test.util.ReflectionTestUtils.setField;

public class CategoryServiceImplTest {
    private Category root;
    Category category1;
    Category category2;
    Category category3;
    private CategoryModel categoryModel;
    private CategoryServiceImpl categoryService;

    @Before
    public void setup() {
        categoryModel = mock(CategoryModel.class);
        root = newCategory(0);
        category1 = newCategory(1);
        category2 = newCategory(2);
        category3 = newCategory(3);
        categoryService = new CategoryServiceImpl(categoryModel);
        setField(categoryService, "categoryModel", categoryModel);
    }

    @Test
    public void shouldConvertModelHierarchyListIntoHierarchyMap() {
        List<Category> hierarchyList = Lists.newArrayList(root, category1, category2, category3);
        given(categoryModel.getFullPath(3L)).willReturn(hierarchyList);

        Map<Integer, Category> hierarchyMap = categoryService.getLevelHierarchy(category3);

        assertThat(hierarchyMap.get(0), equalTo(root));
        assertThat(hierarchyMap.get(1), equalTo(category1));
        assertThat(hierarchyMap.get(2), equalTo(category2));
        assertThat(hierarchyMap.get(3), equalTo(category3));
    }

    private Category newCategory(int id) {
        Category cat = new Category();
        cat.setId((long)id);
        given(categoryModel.getCategory((long) id)).willReturn(Optional.of(cat));
        return cat;
    }
}
