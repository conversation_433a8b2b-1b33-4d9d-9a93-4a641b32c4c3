package com.gumtree.service.category;

import com.gumtree.api.category.domain.Category;
import com.gumtree.service.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.equalToIgnoringCase;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

public class CategoryServiceTest extends BaseTest {

    @Autowired
    private CategoryService categoryService;

    @Test
    public void existsForNullNameShouldReturnFalse() {
        assertThat(categoryService.exists((String)null), equalTo(false));
    }

    @Test
    public void existsForNullIdShouldReturnFalse() {
        assertThat(categoryService.exists((Long)null), equalTo(false));
    }

    @Test
    public void existsForNonExistantShouldReturnFalse() {
        assertThat(categoryService.exists("non-existant"), equalTo(false));
    }

    @Test
    public void existsForExistingShouldReturnTrue() {
        assertThat(categoryService.exists("child-1-1"), equalTo(true));
    }

    @Test
    public void getByIDForNonExistantShouldReturnNull() {
        assertThat(categoryService.getById(100399L).isPresent(), is(false));
    }

    @Test
    public void getByIDForExistingShouldReturnCategory() {
        assertThat(categoryService.getById(1L).get().getSeoName(), equalToIgnoringCase("parent-1"));
    }

    @Test
    public void getChildCategories() {
        Category category = categoryService.getById(1L).get();
        assertThat(categoryService.getChildCategories(category), notNullValue());
        assertThat(categoryService.getChildCategories(category).size(), equalTo(2));
    }

    @Test
    public void isChild() {
        Category parent = categoryService.getById(1L).get();
        Category child = categoryService.getById(3L).get();
        assertThat(categoryService.isChild(child, parent), is(true));
    }

    @Test
    public void isChildNegative() {
        Category parent = categoryService.getById(1L).get();
        Category notDirectChild = categoryService.getById(15L).get();
        assertThat(categoryService.isChild(parent, notDirectChild), is(false));
    }

    @Test
    public void isLeaf() {
        Category parent = categoryService.getById(1L).get();
        Category leaf = categoryService.getById(15L).get();
        assertThat(categoryService.isLeaf(parent), is(false));
        assertThat(categoryService.isLeaf(leaf), is(true));
    }

    @Test
    public void getCategoryLevelReturnsCorrectly() {
        assertThat(categoryService.getCategoryLevel(0L), equalTo(0));
        assertThat(categoryService.getCategoryLevel(1L), equalTo(1));
        assertThat(categoryService.getCategoryLevel(2L), equalTo(1));
        assertThat(categoryService.getCategoryLevel(3L), equalTo(2));
        assertThat(categoryService.getCategoryLevel(4L), equalTo(2));
        assertThat(categoryService.getCategoryLevel(5L), equalTo(2));
        assertThat(categoryService.getCategoryLevel(6L), equalTo(2));
        assertThat(categoryService.getCategoryLevel(7L), equalTo(1));
        assertThat(categoryService.getCategoryLevel(8L), equalTo(2));
        assertThat(categoryService.getCategoryLevel(9L), equalTo(2));
        assertThat(categoryService.getCategoryLevel(10L), equalTo(2));
        assertThat(categoryService.getCategoryLevel(11L), equalTo(2));
        assertThat(categoryService.getCategoryLevel(12L), equalTo(2));
        assertThat(categoryService.getCategoryLevel(13L), equalTo(2));
    }
}
