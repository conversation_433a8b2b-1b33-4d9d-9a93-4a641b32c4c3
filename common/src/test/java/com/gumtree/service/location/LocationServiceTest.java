package com.gumtree.service.location;

import com.gumtree.domain.location.Location;
import com.gumtree.service.BaseTest;
import org.hamcrest.Matchers;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.equalToIgnoringCase;

public class LocationServiceTest extends BaseTest {

    @Autowired
    private LocationService locationService;

    @Test
    public void existsForNullShouldReturnFalse() {
        assertThat(locationService.exists(null), equalTo(false));
    }

    @Test
    public void existsForNonExistantShouldReturnFalse() {
        assertThat(locationService.exists("non-existant"), equalTo(false));
    }

    @Test
    public void existsForExistingShouldReturnTrue() {
        assertThat(locationService.exists("oxford"), equalTo(true));
    }

    @Test
    public void isLandingForLandingLocationShouldReturnTrue() {
        assertThat(locationService.isLanding("london"), equalTo(true));
    }

    @Test
    public void isLandingForNonLandingLocationShouldReturnFalse() {
        assertThat(locationService.isLanding("clapham"), equalTo(false));
    }

    @Test
    public void isLandingForNonExistentLocationShouldReturnFalse() {
        assertThat(locationService.isLanding("non-existant"), equalTo(false));
    }

    @Test
    public void getLandingForLandingLocationShouldReturnTrue() {
        Location location = locationService.getLanding("london");
        assertThat(location.getName(), equalTo("london"));
        assertThat(location.getDisplayName(), equalTo("London"));
    }

    @Test
    public void getLandingForNonLandingLocationShouldReturnFalse() {
        assertThat(locationService.getLanding("clapham"), Matchers.nullValue());
    }

    @Test
    public void getLandingForNonExistentLocationShouldReturnFalse() {
        assertThat(locationService.getLanding("non-existent"), Matchers.nullValue());
    }

    @Test
    public void getCountyForUKShouldReturnUK() {
        Location uk = locationService.getByName("uk");
        assertThat(locationService.getCounty(uk), equalTo(uk));
    }

    @Test
    public void getCountyForFirstLevelLocation() {
        Location london = locationService.getByName("london");
        assertThat(locationService.getCounty(london), equalTo(london));
    }

    @Test
    public void getByIdReturnsCorrectLocation() {
        assertThat(locationService.getById(1).getName(), equalTo("london"));
        assertThat(locationService.getById(2).getName(), equalTo("west-london"));
        assertThat(locationService.getById(17).getName(), equalTo("essex"));
    }

    @Test
    public void getByIdReturnsNullForUnknownLocationID() {
        assertThat(locationService.getById(0), equalTo(null));
    }

    // TODO: remove ignore annotation when zoom in and zoom out issues in database are sorted
    @Ignore("Test will not pass due to missing zoom out entries in the database")
    @Test
    public void getCountyForSecondLevelLocation() {
        Location centralLondon = locationService.getByName("central-london");
        Location london = locationService.getByName("london");
        assertThat(locationService.getCounty(centralLondon), equalTo(london));
    }

    // TODO: remove ignore annotation when zoom in and zoom out issues in database are sorted
    @Ignore("Test will not pass due to missing zoom out entries in the database")
    @Test
    public void getCountyForThirdLevelLocation() {
        Location islington = locationService.getByName("islington");
        Location london = locationService.getByName("london");
        assertThat(locationService.getCounty(islington), equalTo(london));
    }
}
