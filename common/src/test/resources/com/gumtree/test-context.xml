<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
                           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.1.xsd
                           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <context:property-placeholder
            location="classpath:/com/gumtree/test-application.properties"
            ignore-resource-not-found="true"
            system-properties-mode="OVERRIDE"/>

    <tx:annotation-driven/>
    <context:component-scan base-package="com.gumtree.service">
        <context:exclude-filter type="regex" expression="com\.gumtree\.service\.jobs\..*"/>
    </context:component-scan>
    <context:component-scan base-package="com.gumtree.common.format"/>
    <context:component-scan base-package="com.gumtree.common.model"/>
    <context:component-scan base-package="com.gumtree.util"/>
    <context:component-scan base-package="com.gumtree.search.stub"/>
    <context:component-scan base-package="com.gumtree.domain.newattribute.internal"/>
    <context:component-scan base-package="com.gumtree.service.conversion"/>

    <bean class="com.gumtree.api.client.executor.impl.ExceptionHandlingApiCallExecutor" />

    <bean class="com.gumtree.api.config.StubCategoryModelConfig"/>
    <bean class="com.gumtree.api.category.stub.CategoryReadApiSimpleStub"/>

    <bean id="legacySiteMap" class="com.gumtree.legacy.impl.LegacySiteMapImpl"/>

    <bean id="cacheManager" class="org.springframework.cache.ehcache.EhCacheManagerFactoryBean">
        <property name="configLocation" value="classpath:/META-INF/ehcache.xml"/>
    </bean>

    <bean id="siteProperties" class="org.springframework.core.io.ClassPathResource">
        <constructor-arg value="siteId.properties"/>
    </bean>

    <bean id="postingCategoryMappings" class="org.springframework.core.io.ClassPathResource">
        <constructor-arg value="postingCategoryMappings.csv"/>
    </bean>

    <bean id="memcachedClient" class="com.gumtree.util.cache.memcached.MemcachedClientFactoryBean"
          destroy-method="shutdown"/>

    <bean id="jsonAttributeMetadataSource" class="org.springframework.core.io.ClassPathResource">
        <constructor-arg name="path"
                         value="com/gumtree/model/metadata/attribute/impl/json/test-attribute-metadata.json"/>
    </bean>

    <bean id="attributeLabelsProperties" class="org.springframework.beans.factory.config.PropertiesFactoryBean">
        <property name="location" value="classpath:labels/test-attribute-labels.properties"/>
    </bean>

    <bean id="attributeEnumValuesProperties" class="org.springframework.beans.factory.config.PropertiesFactoryBean">
        <property name="location" value="classpath:labels/test-attribute-enum-values.properties"/>
    </bean>

    <bean id="categoryOverrideAttributeLabelsProperties"
          class="org.springframework.beans.factory.config.PropertiesFactoryBean">
        <property name="location" value="classpath:labels/test-category-override-attribute-labels.properties"/>
    </bean>

    <bean id="categoryOverrideAttributeEnumValuesProperties"
          class="org.springframework.beans.factory.config.PropertiesFactoryBean">
        <property name="location" value="classpath:labels/test-category-override-attribute-enum-values.properties"/>
    </bean>

    <bean id="packageProperties" class="org.springframework.beans.factory.config.PropertiesFactoryBean">
        <property name="locations">
            <list>
                <value>classpath:META-INF/maven/gumtree-bushfire/public/pom.properties</value>
            </list>
        </property>
        <property name="ignoreResourceNotFound" value="true"/>
    </bean>

    <bean id="pageConfiguration" class="com.gumtree.common.util.ResourceFactoryBean">
        <constructor-arg name="path" value="bushfirePageConfiguration.properties"/>
        <constructor-arg name="isClassPath" value="true"/>
    </bean>

    <bean id="jobConfiguration" class="com.gumtree.common.util.ResourceFactoryBean">
        <constructor-arg name="path" value="jobsLandingPageCategoryStructure.properties"/>
        <constructor-arg name="isClassPath" value="true"/>
    </bean>


    <bean id="objectMapperFactory" class="com.gumtree.api.util.ObjectMapperFactory"/>
    <bean id="objectMapper" factory-bean="objectMapperFactory" factory-method="create"/>

    <bean id="homepageCategoryConfiguration" class="com.gumtree.common.util.json.JsonObjectFactoryBean">
        <constructor-arg name="objectType"
                         value="com.gumtree.common.model.category.impl.homepage.mapping.LocationHomepageCategoryConfigurationModel"/>
        <property name="jsonResource" value="classpath:/homepage-category-configuration.json"/>
        <property name="objectMapper" ref="objectMapper"/>
    </bean>

    <bean id="bushfireApi" class="com.gumtree.api.client.StubBushfireApi" />

    <bean id="locationApi" class="com.gumtree.util.stub.api.StubLocationApi">
        <constructor-arg ref="locationsModel"/>
        <constructor-arg ref="outcodesModel"/>
    </bean>

    <bean id="accountApi" class="com.gumtree.api.client.stub.StubAccountApi" />
    <bean id="advertApi" class="com.gumtree.util.stub.api.StubAdvertApi" />
    <bean id="orderApi" class="com.gumtree.util.stub.api.StubOrderApi" />
    <bean id="userApi" class="com.gumtree.api.client.stub.StubUserApi" />

    <bean class="com.gumtree.api.config.StubCategoryModelConfig"/>
    <bean id="categoriesResource" class="org.springframework.core.io.ClassPathResource">
        <constructor-arg name="path" value="/stub/category/category-tree.json"/>
    </bean>

    <bean id="locationsModel" class="com.gumtree.common.util.json.JsonObjectFactoryBean">
        <constructor-arg name="objectType" value="com.gumtree.api.Locations"/>
        <property name="jsonResource" value="classpath:stub/location/locations.json"/>
        <property name="objectMapper" ref="objectMapper"/>
    </bean>

    <bean id="outcodesModel" class="com.gumtree.common.util.json.JsonObjectFactoryBean">
        <constructor-arg name="objectType" value="com.gumtree.api.Outcodes"/>
        <property name="jsonResource" value="classpath:stub/location/outcodes.json"/>
        <property name="objectMapper" ref="objectMapper"/>
    </bean>

    <bean id="exceptionTranslator" class="com.gumtree.api.client.util.BushfireApiExceptionTranslator"/>
    <bean id="conversionService" class="org.springframework.context.support.ConversionServiceFactoryBean"/>
    <bean id="secureImageService" class="com.gumtree.web.service.images.secure.DefaultSecureImageService">
        <constructor-arg type="java.lang.String" value="https://i.ebayimg.sandbox.ebay.com"/>
    </bean>

</beans>