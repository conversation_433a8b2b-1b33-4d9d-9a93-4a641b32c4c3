// Simple test to verify our understanding
public class SimpleTest {
    public static void main(String[] args) {
        System.out.println("Testing invalidateThreatMetrixCookie method");
        
        // Test scenarios:
        // 1. When disabled - no cookies added
        // 2. When cookie exists with different domain - 2 cookies added
        // 3. When cookie exists with same domain - 1 cookie added
        // 4. When cookie exists without domain - 1 cookie added
        // 5. When no cookie exists - no cookies added
        
        System.out.println("All scenarios covered in unit tests");
    }
}
