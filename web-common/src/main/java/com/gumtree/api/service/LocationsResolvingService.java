package com.gumtree.api.service;

import com.gumtree.api.bapi.BapiLocationsService;
import com.gumtree.api.command.locationservice.LocationFindLikeCommand;
import com.gumtree.api.command.locationservice.LocationFindPrimaryPostcodeForLocationCommand;
import com.gumtree.api.command.locationservice.LocationFindPrimaryPostcodeForOutcodeCommand;
import com.gumtree.api.command.locationservice.LocationResolveOutcodeCommand;
import com.gumtree.api.command.locationservice.LocationResolvePostcodeCommand;
import com.gumtree.api.locations.infrastructure.LocationsApi;
import com.gumtree.api.locations.model.LocationData;
import com.gumtree.api.locations.model.SuggestResponse;
import com.gumtree.common.util.location.LocationUtils;
import com.gumtree.common.util.url.UrlUtils;
import com.gumtree.web.common.domain.location.Location;
import com.gumtree.web.common.domain.location.ResolvedLocation;
import com.gumtree.web.common.domain.location.ResolvedNamedLocation;
import com.gumtree.web.common.domain.location.ResolvedOutcodeLocation;
import com.gumtree.web.common.domain.location.ResolvedPostcodeLocation;
import com.gumtree.web.common.domain.location.UnresolvedLocation;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.Validate;
import org.springframework.cache.annotation.Cacheable;
import rx.Single;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

public class LocationsResolvingService implements LocationsService {
    private static final SuggestResponse EMPTY_SUGGEST_RESPONSE = new SuggestResponse().options(Collections.emptyList());

    private final BapiLocationsService bapiLocationsService;
    private final LocationsApi locationsApi;

    public LocationsResolvingService(BapiLocationsService bapiLocationsService, LocationsApi locationsApi) {
        this.bapiLocationsService = bapiLocationsService;
        this.locationsApi = locationsApi;
    }

    @Override
    public Location getRoot() {
        return bapiLocationsService.getRoot();
    }

    @Override
    public com.google.common.base.Optional<Location> getLocation(String idName) {
        return bapiLocationsService.getLocation(idName);
    }

    @Override
    public com.google.common.base.Optional<Location> getLocation(Long id) {
        return bapiLocationsService.getLocation(id);
    }

    @Override
    public Location getBestMatch(Long id) {
        return bapiLocationsService.getBestMatch(id);
    }

    @Override
    public Collection<Location> getAll() {
        return bapiLocationsService.getAll();
    }

    @Override
    public Single<SuggestResponse> searchLocations(String input) {
        if (StringUtils.isBlank(input) || input.trim().length() < 2) {
            return Single.just(EMPTY_SUGGEST_RESPONSE);
        }

        return new LocationFindLikeCommand(input, locationsApi).toObservable().toSingle();
    }

    @Override
    public Location findBestMatch(String locationTerm) {
        ResolvedLocation resolvedLocation = resolveLocation(locationTerm).toBlocking().value();
        return resolvedLocation.getLocation().isPresent() ? resolvedLocation.getLocation().get() : getRoot();
    }

    @Override
    public Single<ResolvedLocation> resolveLocation(String locationTerm) {
        if (locationTerm == null) return Single.just(new UnresolvedLocation(null));

        // speed up resolution: 'uk' looks like outcode
        if ("uk".equals(locationTerm.toLowerCase())) {
            return Single.just(new ResolvedNamedLocation(bapiLocationsService.getRoot()));
        }

        final String encodedLocationTerm = UrlUtils.enc(locationTerm);

        Single<Optional<ResolvedLocation>> resolvedPostcode = LocationUtils.looksLikePostcode(encodedLocationTerm)
                ? resolvePostcode(locationTerm)
                : Single.just(Optional.empty());

        return resolvedPostcode
                .flatMap(postcode -> {
                    if (postcode.isPresent()) {
                        return Single.just(postcode);
                    }
                    return LocationUtils.looksLikeOutcode(encodedLocationTerm)
                            ? resolveOutcode(locationTerm)
                            : Single.just(Optional.<ResolvedLocation>empty());
                })
                .flatMap(postcodeOrOutcode -> {
                    if (postcodeOrOutcode.isPresent()) {
                        return Single.just(postcodeOrOutcode.get());
                    }
                    return findNamedLocation(locationTerm)
                            .map(location ->
                            location.<ResolvedLocation>map(ResolvedNamedLocation::new)
                                    .orElseGet(() -> new UnresolvedLocation(locationTerm)));
                });
    }

    @Override
    public com.google.common.base.Optional<Location> getLocationByDisplayName(String displayName) {
        return bapiLocationsService.getLocationByDisplayName(displayName);
    }

    @Override
    public ResolvedLocation resolveLocation(long id) {
        return bapiLocationsService.resolveLocation(id);
    }

    @Cacheable(value = "primaryPostcodeForLocation", key = "'primaryPostcodeForLocation' +#locationId")
    @Override
    public Optional<String> findPrimaryPostcodeForLocation(long locationId) {
        Validate.isTrue(locationId != com.gumtree.domain.location.Location.UK_ID);

        Optional<LocationData> location = new LocationFindPrimaryPostcodeForLocationCommand(locationId, locationsApi).execute();
        return location.map(LocationData::getPostcode);
    }

    @Cacheable(value = "primaryPostcodeForOutcode", key = "'primaryPostcodeForOutcode_' +#outcode")
    @Override
    public Optional<String> findPrimaryPostcodeForOutcode(String outcode) {
        Optional<LocationData> outcodeRes = new LocationFindPrimaryPostcodeForOutcodeCommand(outcode, locationsApi).execute();
        return outcodeRes.map(LocationData::getPostcode);
    }

    @Override
    public String getDisplayName(Location location) {
        Validate.notNull(location);

        return bapiLocationsService.getDisplayName(location.getId());
    }

    @Override
    public com.google.common.base.Optional<Location> get(Level desiredLevel, Long id) {
        return bapiLocationsService.get(desiredLevel, id);
    }

    @Nonnull
    @Override
    public List<Location> getHierarchy(@Nonnull Location location) {
        return bapiLocationsService.getHierarchy(location);
    }

    private Single<Optional<Location>> findNamedLocation(String locationTerm) {
        final com.google.common.base.Optional<Location> locationOpt = getLocation(locationTerm)
                .or(bapiLocationsService.getLocationByDisplayName(locationTerm));
        return locationOpt.isPresent() ? Single.just(Optional.of(locationOpt.get())) : getBestMatch(locationTerm);
    }

    private Single<Optional<Location>> getBestMatch(String input) {
        if (StringUtils.isBlank(input)) {
            return Single.just(Optional.empty());
        }

        return searchLocations(input)
                .map(matches -> matches.getOptions().stream() //// getBestMatch only on first location [not out/postcode]
                        .filter(e -> LocationData.TypeEnum.LOCATION == e.getType())
                        .map(e -> getLocation(e.getId()).get())
                        .findFirst());
    }

    private Single<Optional<ResolvedLocation>> resolvePostcode(String input) {
        return new LocationResolvePostcodeCommand(input, locationsApi).toObservable().toSingle()
                .map(result -> result.map(r -> new ResolvedPostcodeLocation(
                        r.getName(),
                        new BigDecimal(r.getLatitude()),
                        new BigDecimal(r.getLongitude()),
                        getLocation(r.getLocationId()))));
    }

    private Single<Optional<ResolvedLocation>> resolveOutcode(String input) {
        return new LocationResolveOutcodeCommand(input, locationsApi).toObservable().toSingle()
                .map(result -> result.map(r -> new ResolvedOutcodeLocation(
                        r.getName(),
                        new BigDecimal(r.getLatitude()),
                        new BigDecimal(r.getLongitude()),
                        getLocation(r.getLocationId())))
                );
    }


}
