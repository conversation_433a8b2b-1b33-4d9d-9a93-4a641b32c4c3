package com.gumtree.api.service;

import com.google.common.base.Optional;
import com.gumtree.api.locations.model.SuggestResponse;
import com.gumtree.web.common.domain.location.Location;
import com.gumtree.web.common.domain.location.ResolvedLocation;
import rx.Single;

import javax.annotation.Nonnull;
import java.util.Collection;
import java.util.List;

public interface LocationsService {

    enum Level {
        COUNTRY(1),
        COUNTY(2),
        REGION(3),
        LOCATION(4);

        private int level;

        Level(int level) {
            this.level = level;
        }

        public int getLevel() {
            return level;
        }
    }

    Long[] TOP_CITIES = {10000355L, 10000347L, 10000350L, 10000348L, 10000354L,
            10000345L, 10000349L, 10000360L, 10000362L, 10000344L, 10000352L, 10000370L};

    /**
     * Used for autoreloading of the locations every x minutes via {@code @Scheduled}}
     */

    Location getRoot();

    Optional<Location> getLocation(String idName);

    Optional<Location> getLocation(Long id);

    Optional<Location> getLocationByDisplayName(String displayName);

    /**
     * Tries to find a location with the given id but if no location is found that it returns the root location
     *
     * @param id the location id
     * @return the location
     */
    Location getBestMatch(Long id);

    Collection<Location> getAll();

    Single<SuggestResponse> searchLocations(String input);

    Single<ResolvedLocation> resolveLocation(String locationTerm);

    ResolvedLocation resolveLocation(long id);

    java.util.Optional<String> findPrimaryPostcodeForLocation(long locationId);

    java.util.Optional<String> findPrimaryPostcodeForOutcode(String outcode);

    /**
     * Tries to resolve location term into a location if no location is found then it return root location
     *
     * @param locationTerm the location term
     * @return the location matching the term or root location if no matching location is found
     */
    Location findBestMatch(String locationTerm);

    String getDisplayName(Location location);

    Optional<Location> get(Level desiredLevel, Long id);

    /**
     * Gets ancestors and location itself
     *
     * @param location starting location
     * @return list of locations with UK at the beginning down to given location (included), Exmaple for London: [UK, Endland, London]
     */
    @Nonnull
    List<Location> getHierarchy(@Nonnull Location location);
}
