package com.gumtree.seo;

import java.util.ArrayList;
import java.util.List;

public enum SEOPageProperty {

    CATEGORY("[Category]", "category"),
    LOCATION("[Location]", "location"),
    PAGINATION("[x]/[x]", "pagination"),
    AD_COUNT("[Ad-count]"),
    AD_TITLE("[Ad-title]"),
    AD_COPY("[Ad-copy]"),
    SEARCH_TERM("[Search-term]", "search-term"),
    BLANK_SEARCH_TERM(null, "blank-search"),
    VIP(null, "vip"),
    EMPLOYER("[employer]", "employer"),
    CAR_MAKE("[Car-make]", "vehicle-make"),
    CAR_MODEL("[Car-model]", "vehicle-model"),
    BIKE_MAKE("[Motorbike-make]", "motorbike-make"),
    POPULAR_SEARCH(null, "popular-search");

    public static final List<SEOPageProperty> PAGE_ID_PROPERTIES = pageIdProperties();

    private String placeHolder;
    private String key;

    private SEOPageProperty(String placeHolder, String key) {
        this.placeHolder = placeHolder;
        this.key = key;
    }

    private SEOPageProperty(String placeHolder) {
        this.placeHolder = placeHolder;
    }

    public String getPlaceHolder() {
        return placeHolder;
    }

    public String getKey() {
        return key;
    }

    public boolean hasPlaceholder() {
        return placeHolder != null;
    }

    public boolean hasKey() {
        return key != null;
    }

    private static List<SEOPageProperty> pageIdProperties() {
        List<SEOPageProperty> result = new ArrayList<SEOPageProperty>();
        for (SEOPageProperty property: SEOPageProperty.values()) {
            if (property.getKey() != null) {
                result.add(property);
            }
        }

        return result;
    }
}
