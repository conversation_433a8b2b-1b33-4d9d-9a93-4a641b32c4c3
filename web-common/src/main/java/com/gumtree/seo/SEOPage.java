package com.gumtree.seo;

import com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class SEOPage {
    public static final String SEO_TITLE = "seoTitle";
    public static final String SEO_DESCRIPTION = "seoDescription";
    public static final String SEO_H1 = "seoH1";

    private Map<String, String> properties;

    public SEOPage(Map<String, String> properties) {
        this.properties = properties;
    }

    public String getTitle() {
        return getProperty(SEO_TITLE, "Gumtree.com");
    }

    public String getDescription() {
        return getProperty(SEO_DESCRIPTION, "Gumtree.com");
    }

    public String getH1() {
        return getProperty(SEO_H1, "");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        SEOPage seoPage = (SEOPage) o;

        if (properties != null ? !properties.equals(seoPage.properties) : seoPage.properties != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        return properties != null ? properties.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "SEOPage{"
                + "properties=" + properties
                + '}';
    }

    private String getProperty(String name, String defaultValue) {
        String value = properties.get(name);
        return value == null ? defaultValue : value;
    }

    public static class Builder {
        private Map<String, String> placeholders;
        private Set<String> pageId;
        private List<String> categoryHierarchy;

        private CategorySeoConfig categorySeoConfig;

        public Builder() {
            placeholders = new HashMap<String, String>();
            pageId = new HashSet<String>();
        }

        public Builder withCategoryHierarchy(List<String> categoryHierarchy) {
            this.categoryHierarchy = categoryHierarchy;
            return this;
        }

        public Builder withCategoryConfig(CategorySeoConfig categoryConfig) {
            this.categorySeoConfig = categoryConfig;
            return this;
        }

        public SEOPage build() {
            Assert.notNull(categoryHierarchy);
            return new SEOPage(buildPageProperties());
        }

        public Map<String, String> getPlaceholders() {
            return placeholders;
        }

        public List<String> getCategoryHierarchy() {
            return categoryHierarchy;
        }

        public CategorySeoConfig getCategorySeoConfig() {
            return categorySeoConfig;
        }


        private Map<String, String> buildPageProperties() {
            Map<String, String> result = new HashMap<String, String>();
            Map<String, String> categoryConfig = categorySeoConfig.getPageConfig(categoryHierarchy, pageId);

            if (categoryConfig != null && !categoryConfig.isEmpty() && !placeholders.isEmpty()) {
                for (Map.Entry<String, String> entry : categoryConfig.entrySet()) {
                    result.put(entry.getKey(), replacePlaceHolders(entry.getValue(), placeholders));
                }
            }

            return result;
        }

        public static String replacePlaceHolders(String textWithPlaceholders, Map<String, String> placeholders) {
            if (StringUtils.isBlank(textWithPlaceholders)) {
                return "";
            }


            for (Map.Entry<String, String> entry : placeholders.entrySet()) {
                textWithPlaceholders = textWithPlaceholders.replace(entry.getKey(), entry.getValue());
            }

            // remove all placeholders that were not replaced
            for (SEOMetadataProperty seoProp: SEOMetadataProperty.values()) {
                if (seoProp.getPlaceHolder() != null) {
                    textWithPlaceholders = textWithPlaceholders.replace(seoProp.getPlaceHolder(), "");
                }
            }

            return textWithPlaceholders.trim();
        }
    }
}
