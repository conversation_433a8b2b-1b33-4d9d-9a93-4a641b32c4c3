package com.gumtree.web.common.domain.converter;

import com.google.common.base.Optional;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.category.domain.display.AttributeDisplayMetadata;
import com.gumtree.api.category.domain.display.DisplayAttributeValueMetadata;
import com.gumtree.web.common.domain.ad.Advert;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.DateTimeFormatterBuilder;
import org.joda.time.format.DateTimeParser;
import org.joda.time.format.ISODateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public abstract class Conversions {
    public static final Logger LOGGER = LoggerFactory.getLogger(Conversions.class);

    private static final DateTimeParser[] DATE_TIME_PARSERS = {DateTimeFormat.forPattern("yyyyMMdd").getParser(),
            ISODateTimeFormat.dateHourMinuteSecond().getParser()};
    private static final DateTimeFormatter FROM_DATE_FORMATTER = new DateTimeFormatterBuilder()
            .append(null, DATE_TIME_PARSERS).toFormatter();
    private static final DateTimeFormatter TO_DATE_FORMATTER = DateTimeFormat.forPattern("dd MMM yyy");

    private static final Pattern YOUTUBE_LINK_PATTERN =
            Pattern.compile("https?://(?:youtu\\.be/|www\\.youtube\\.com/(?:watch\\?|v/))(\\S+)");

    public static final String CURRENCY_SYMBOL = "£";
    private static final String CURRENCY_PATTERN = "#,###.##";
    private static final int CURRENCY_FRACTIONS = 2;
    private static final BigDecimal ONE_HUNDRED = new BigDecimal("100");
    private static final Optional<String> YES = Optional.of("Yes");
    private static final Optional<String> NO = Optional.of("No");

    private Conversions() {
    }

    public interface AttributeBuilder<T> {
        T build();

        AttributeBuilder<T> setValue(String value);

        AttributeBuilder<T> setValueDisplayName(String value);

        AttributeBuilder<T> setAttributeMetadata(AttributeMetadata metadata);
    }

    public static <T> Optional<T> createAttribute(String attrName,
                                                  Map<String, String> attributes,
                                                  Collection<AttributeMetadata> metadata,
                                                  AttributeBuilder<T> builder) {
        return createAttribute(attrName, attributes.get(attrName), metadata, builder);
    }

    public static <T> Optional<T> createAttribute(CategoryConstants.Attribute attrName,
                                                  String attrValue,
                                                  Collection<AttributeMetadata> metadata,
                                                  AttributeBuilder<T> builder) {
        return createAttribute(attrName.getName(), attrValue, metadata, builder);
    }

    public static Optional<BigDecimal> tryString2BigDecimal(@Nonnull String value) {
        Assert.notNull(value);
        try {
            return Optional.fromNullable(new BigDecimal(value).setScale(2, BigDecimal.ROUND_FLOOR));
        } catch (Exception ex) {
            // catching Java number format exception here for when we get "_" from legacy
            return Optional.absent();
        }
    }

    public static BigDecimal poundsToPence(@Nonnull BigDecimal pounds) {
        Assert.notNull(pounds);
        return pounds.setScale(2, BigDecimal.ROUND_FLOOR).multiply(ONE_HUNDRED);
    }

    public static Optional<BigDecimal> pounds2Pence(@Nonnull String value) {
        Assert.notNull(value);
        return tryString2BigDecimal(value).transform(Conversions::poundsToPence);
    }

    /**
     * Could be more precise by checking if current month is after middle of the year
     */
    public static Long age2Year(@Nonnull BigDecimal ageInYears, int currentYear) {
        Assert.notNull(ageInYears);
        int ageInYearsInt = ageInYears.intValue();
        Assert.isTrue(ageInYearsInt >= 0);

        return ageInYearsInt > 0
                ? currentYear - ageInYearsInt + 1
                : (long) currentYear;
    }

    public static String buildYoutubeLink(String youtubeLink) {
        if (youtubeLink != null) {
            String params = "";
            // match new and old youtube link formats
            Matcher matcher = YOUTUBE_LINK_PATTERN.matcher(youtubeLink);
            //check if the link provided matches
            if (matcher.matches()) {
                // this will contain the video id plus additional params.
                params = matcher.group(1);
                // due to the different url formats for youtube, the video id may be marked by 'v' or have no key
                params = params.replaceAll("v=", "");
                // if additional params exist but no ? in the url, create one in the place of first &
                if (params.contains("&") && !params.contains("?")) {
                    params = params.replaceFirst("&", "?");
                }

            }
            // build embed link
            return "//www.youtube.com/embed/" + params;
        } else {
            return null;
        }
    }

    public static Advert.Status getAdvertStatus(String status) {
        if (status == null) {
            return Advert.Status.UNKNOWN;
        }
        switch (status) {
            case "LIVE":
                return Advert.Status.LIVE;
            case "NEEDS_EDITING":
                return Advert.Status.NEEDS_EDITING;
            case "DRAFT":
                return Advert.Status.DRAFT;
            case "AWAITING_CS_REVIEW":
                return Advert.Status.AWAITING_CS_REVIEW;
            case "EXPIRED":
                return Advert.Status.EXPIRED;
            case "DELETED_USER":
                return Advert.Status.DELETED_USER;
            case "PRE_PUBLISHED":
                return Advert.Status.PRE_PUBLISHED;
            case "DELETED_CS":
                return Advert.Status.DELETED_CS;
            default:
                return Advert.Status.UNKNOWN;
        }
    }

    public static Optional<String> getAttributeValueLabel(String attrValue,
                                                          AttributeMetadata metadata) {
        Assert.notNull(attrValue);
        Assert.notNull(metadata);

        switch (metadata.getType()) {
            case ENUM:
                return findEnumValueLabel(attrValue, metadata.getDisplay().getValues());
            case CURRENCY:
                return formatPrice(attrValue);
            case DATETIME:
                return formatDate(attrValue, metadata);
            case BOOLEAN:
                return getFormattedBoolean(attrValue);
            default:
                return findEnumValueLabel(attrValue, metadata.getDisplay().getValues());
        }
    }

    public static <T> Optional<T> createAttribute(String attrName,
                                                  String attrValue,
                                                  Collection<AttributeMetadata> metadata,
                                                  AttributeBuilder<T> builder) {
        Assert.notNull(attrName);
        Assert.notNull(builder);
        if (attrValue != null && metadata != null && !metadata.isEmpty()) {
            for (AttributeMetadata attrMeta : metadata) {
                if (attrName.equalsIgnoreCase(attrMeta.getName())) {
                    Optional<String> valueLabel = getAttributeValueLabel(attrValue, attrMeta);

                    builder.setValue(attrValue);
                    builder.setValueDisplayName(valueLabel.or(attrValue));
                    builder.setAttributeMetadata(attrMeta);
                    return Optional.of(builder.build());
                }
            }
        }

        return Optional.absent();
    }

    private static Optional<String> findEnumValueLabel(String value, List<DisplayAttributeValueMetadata> metadataValues) {
        if (metadataValues != null && !metadataValues.isEmpty()) {
            for (DisplayAttributeValueMetadata metadataValue : metadataValues) {
                if (value.equalsIgnoreCase(metadataValue.getValue())) {
                    return Optional.fromNullable(metadataValue.getLabel());
                }
            }
        }

        return Optional.absent();
    }

    private static Optional<String> formatPrice(String value) {
        Long amount;
        try {
            amount = Long.parseLong(value);
            DecimalFormat currencyFormat = new DecimalFormat(CURRENCY_PATTERN);
            currencyFormat.setMaximumFractionDigits(CURRENCY_FRACTIONS);
            currencyFormat.setMinimumFractionDigits(CURRENCY_FRACTIONS);
            BigDecimal adjustedPrice = new BigDecimal(amount).divide(ONE_HUNDRED);
            return Optional.of(CURRENCY_SYMBOL + currencyFormat.format(adjustedPrice));
        } catch (NumberFormatException nfe) {
            LOGGER.debug("Number format exceptions " + nfe);
        }
        return Optional.absent();
    }

    /**
     * Formats currency in pence to pounds format in short format.
     * <p>
     * Short format collapses multiple 0s to as little 0s as possible:
     * 100 pence = 1 pound
     * 1000 pence = 10 pounds
     * 100000 pence = 1k pounds
     * 1000000 pence = 10k pounds
     * <p>
     * not collapsable
     * 150 pence = 1.50 pound
     * 10099 = 100.99 pounds
     * 100099 = 1,000.99 pounds
     *
     * @param value the amount in pence to format
     * @return the value in pounds formatted
     */
    public static java.util.Optional<String> fromPence2PoundsWithZerosCollapsed(String value) {
        Long amount;
        try {
            amount = Long.parseLong(value);
            DecimalFormat currencyFormat = new DecimalFormat(CURRENCY_PATTERN);
            if (amount == 0) {
                return java.util.Optional.of(CURRENCY_SYMBOL + "0");
            } else if (amount % 100000 == 0) {
                return java.util.Optional.of(CURRENCY_SYMBOL + currencyFormat.format(amount / 100000) + "k");
            } else if (amount % 100 == 0) {
                return java.util.Optional.of(CURRENCY_SYMBOL + currencyFormat.format(amount / 100));
            } else {
                currencyFormat.setMaximumFractionDigits(CURRENCY_FRACTIONS);
                currencyFormat.setMinimumFractionDigits(CURRENCY_FRACTIONS);
                BigDecimal adjustedPrice = new BigDecimal(amount).divide(ONE_HUNDRED);
                return java.util.Optional.of(CURRENCY_SYMBOL + currencyFormat.format(adjustedPrice));
            }
        } catch (NumberFormatException nfe) {
            LOGGER.debug("Number format exceptions " + nfe);
        }
        return java.util.Optional.empty();
    }

    /**
     * Formats currency in pence to pounds format in medium format: don't display pence if .00
     * 100 pence = 1 pound
     * 1000 pence = 10 pounds
     * 100000 pence = 1,000 pounds
     * 1000000 pence = 10,000 pounds
     * <p>
     * not collapsable
     * 150 pence = 1.50 pound
     * 10099 = 100.99 pounds
     * 100099 = 1,000.99 pounds
     *
     * @param amount the amount in pence to format
     * @return the value in pounds formatted
     */
    public static String fromPence2PoundsWithNoCollapsedZeros(long amount) {
        DecimalFormat currencyFormat = new DecimalFormat(CURRENCY_PATTERN);
        if (amount == 0) {
            return CURRENCY_SYMBOL + "0";
        }
        if (amount % 100 == 0) {
            return CURRENCY_SYMBOL + currencyFormat.format(amount / 100);
        }
        currencyFormat.setMaximumFractionDigits(CURRENCY_FRACTIONS);
        currencyFormat.setMinimumFractionDigits(CURRENCY_FRACTIONS);
        BigDecimal adjustedPrice = new BigDecimal(amount).divide(ONE_HUNDRED);
        return CURRENCY_SYMBOL + currencyFormat.format(adjustedPrice);
    }

    public static BigDecimal convertPenceToPounds(Long pence) {
        if (pence != null) {
            return new BigDecimal(pence)
                    .divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_EVEN);
        } else {
            return null;
        }
    }

    private static Optional<String> formatDate(String date, AttributeMetadata metadata) {
        if (metadata.getDisplay().hasValues()) {
            Optional<AttributeDisplayMetadata.MatchResult> matchedValue = metadata.getDisplay().findValue(date);
            return matchedValue.transform(AttributeDisplayMetadata.MatchResult::getLabel);
        } else {
            DateTime fromDate = FROM_DATE_FORMATTER.parseDateTime(date);
            return Optional.of(TO_DATE_FORMATTER.print(fromDate));
        }
    }

    private static Optional<String> getFormattedBoolean(String bool) {
        if (Boolean.parseBoolean(bool)) {
            return YES;
        } else {
            return NO;
        }
    }
}
