package com.gumtree.web.common.domain.order.converter;

import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.web.common.domain.order.Order;

/**
 */
public interface ApiOrderToOrderEntityConverter {

    /**
     * Convert an order retrieved from the api to an order containing exploded adverts
     *
     * @param order returned from the api
     * @return order with exploded adverts
     */
    Order convert(ApiOrder order);
}
