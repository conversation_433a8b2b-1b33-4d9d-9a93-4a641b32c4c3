package com.gumtree.web.common.domain.location;

import com.google.common.base.Optional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Objects;

public class ResolvedPostcodeLocation extends ResolvedLocation implements GeoPointLocation {
    private BigDecimal latitude;
    private BigDecimal longitude;
    private Optional<Location> location;
    private String postcode;

    public ResolvedPostcodeLocation(String postcode, BigDecimal latitude, BigDecimal longitude, Optional<Location> location) {
        Assert.notNull(postcode);
        Assert.notNull(latitude);
        Assert.notNull(longitude);
        Assert.notNull(location);

        this.postcode = postcode.trim().replaceAll("\\s", "");
        this.latitude = latitude;
        this.longitude = longitude;
        this.location = location;
    }

    @Override
    public boolean isPostcode() {
        return true;
    }

    @Override
    public Optional<Location> getLocation() {
        return location;
    }

    @Override
    public String getIdName() {
        return postcode.toLowerCase();
    }

    @Override
    public String getSearchLocation() {
        return postcode.toUpperCase();
    }

    @Override
    public java.util.Optional<BigDecimal> getLatitude() {
        return java.util.Optional.ofNullable(latitude);
    }

    @Override
    public java.util.Optional<BigDecimal> getLongitude() {
        return java.util.Optional.ofNullable(longitude);
    }

    @Override
    public boolean supportsNearbySearch() {
        return true;
    }

    @Override
    public boolean supportsRadialSearch() {
        return true;
    }

    @Override
    public double getRadius() {
        return 0;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ResolvedPostcodeLocation that = (ResolvedPostcodeLocation) o;
        return Objects.equals(latitude, that.latitude) &&
                Objects.equals(longitude, that.longitude) &&
                Objects.equals(location, that.location) &&
                Objects.equals(postcode, that.postcode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(latitude, longitude, location, postcode);
    }

    @Override
    public String toString() {
        return "ResolvedPostcodeLocation{" +
                "latitude=" + latitude +
                ", longitude=" + longitude +
                ", location=" + location +
                ", postcode='" + postcode + '\'' +
                '}';
    }
}
