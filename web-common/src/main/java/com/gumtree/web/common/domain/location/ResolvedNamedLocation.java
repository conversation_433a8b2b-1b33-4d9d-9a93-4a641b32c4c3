package com.gumtree.web.common.domain.location;

import com.google.common.base.Optional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Objects;

public class ResolvedNamedLocation extends ResolvedLocation implements GeoPointLocation {

    private final Optional<Location> location;

    public ResolvedNamedLocation(Location location) {
        Assert.notNull(location);
        this.location = Optional.of(location);
    }

    @Override
    public boolean isLocation() {
        return true;
    }

    public java.util.Optional<BigDecimal> getLatitude() {
        return java.util.Optional.ofNullable(location.get().getLatitude());
    }

    public java.util.Optional<BigDecimal> getLongitude() {
        return java.util.Optional.ofNullable(location.get().getLongitude());
    }

    @Override
    public String getIdName() {
        return location.get().getSeoName();
    }

    @Override
    public String getSearchLocation() {
        return location.get().isLocalityUnique() ? location.get().getLocality() : location.get().getName();
    }

    @Override
    public Optional<Location> getLocation() {
        return location;
    }

    @Override
    public boolean supportsNearbySearch() {
        return location.get().getLatitude() != null && location.get().getLongitude() != null;
    }

    @Override
    public boolean supportsRadialSearch() {
        return location.get().getLatitude() != null && location.get().getLongitude() != null;
    }

    @Override
    public double getRadius() {
        if (location.isPresent() && location.get().getRadius() != null) {
            return location.get().getRadius().doubleValue();
        }
        return 0d;
    }

    public static BigDecimal getNonNullRadius(Location location) {
        return location.getRadius() == null ? BigDecimal.ZERO : location.getRadius();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ResolvedNamedLocation that = (ResolvedNamedLocation) o;
        return Objects.equals(location, that.location);
    }

    @Override
    public int hashCode() {
        return Objects.hash(location);
    }

    @Override
    public String toString() {
        return "ResolvedNamedLocation{" +
                "location=" + location +
                '}';
    }
}
