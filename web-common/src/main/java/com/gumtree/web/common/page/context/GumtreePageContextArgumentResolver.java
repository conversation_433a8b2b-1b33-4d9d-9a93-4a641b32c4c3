package com.gumtree.web.common.page.context;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * Allows for controller methods annotated with {@link org.springframework.web.bind.annotation.RequestMapping}
 * to include a {@link GumtreePageContext} in the method parameters.
 */
public final class GumtreePageContextArgumentResolver implements HandlerMethodArgumentResolver {

    @Autowired
    private GumtreePageContext pageContext;

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.getParameterType().equals(GumtreePageContext.class);
    }

    @Override
    public Object resolveArgument(
            MethodParameter methodParameter,
            ModelAndViewContainer modelAndViewContainer,
            NativeWebRequest nativeWebRequest,
            WebDataBinderFactory webDataBinderFactory) throws Exception {

        if (!pageContext.isInitialised()) {
            throw new IllegalStateException("Page context has not been initialised");
        }

        return pageContext;
    }
}
