package com.gumtree.web.common.domain.distance;

import com.google.common.collect.Lists;
import com.gumtree.web.common.enums.Distance;

import java.util.List;
import java.util.Objects;

public final class DistanceOption {
    private final Double value;
    private final String displayValue;
    private final boolean selected;

    DistanceOption(Double value, String displayValue, boolean isSelected) {
        this.value = value;
        this.displayValue = displayValue;
        this.selected = isSelected;
    }

    public Double getValue() {
        return value;
    }

    public String getDisplayValue() {
        return displayValue;
    }

    /**
     * Allows to create  option that is not selectable by user: selected & disabled
     * Note: flag is currently not utilized in back-end but it's poping up every now
     * and then in requirements and there is fron-end code for it
     */
    public boolean isDisabled() {
        return false;
    }

    public boolean isSelected() {
        return selected;
    }

    /**
     * Generates distance options list and selects one of them, either selected by user or the first one
     *
     * @return select options
     */
    public static List<DistanceOption> generateOptions(Double distance) {
        // find and select distance selected by user or first one
        int index = 0;
        for (int i = 0; i < Distance.values().length; i++) {
            if (Distance.values()[i].getValue().intValue() == distance.intValue()) {
                index = i;
                break;
            }
        }

        List<DistanceOption> options = Lists.newArrayList();
        for (int i = 0; i < Distance.values().length; i++) {
            options.add(new DistanceOption(Distance.values()[i].getValue(), Distance.values()[i].getDisplayValue(), i == index));
        }
        return options;
    }

    @Override
    public String toString() {
        return "DistanceOption{" +
                "value=" + value +
                ", displayValue='" + displayValue + '\'' +
                ", selected=" + selected +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DistanceOption that = (DistanceOption) o;
        return selected == that.selected &&
                Objects.equals(value, that.value) &&
                Objects.equals(displayValue, that.displayValue);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value, displayValue, selected);
    }
}
