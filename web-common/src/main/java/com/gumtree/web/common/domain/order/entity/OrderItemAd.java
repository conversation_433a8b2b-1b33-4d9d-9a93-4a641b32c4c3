package com.gumtree.web.common.domain.order.entity;

import java.util.List;

public final class OrderItemAd {
    private Long id;
    private String title;
    private String location;
    private List<OrderItemProduct> products;

    private OrderItemAd(Builder builder) {
        this.id = builder.id;
        this.title = builder.title;
        this.location = builder.location;
        this.products = builder.products;
    }

    public Long getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public String getLocation() {
        return location;
    }

    public List<OrderItemProduct> getProducts() {
        return products;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private Long id;
        private String title;
        private String location;
        private List<OrderItemProduct> products;

        public Builder withId(Long id) {
            this.id = id;
            return this;
        }

        public Builder withTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder withLocation(String location) {
            this.location = location;
            return this;
        }

        public Builder withProducts(List<OrderItemProduct> products) {
            this.products = products;
            return this;
        }

        public OrderItemAd build() {
            return new OrderItemAd(this);
        }
    }
}
