package com.gumtree.web.common.util;

import com.gumtree.location.LocationDisplayUtils;
import com.gumtree.domain.location.Location;
import com.gumtree.service.location.LocationService;

import java.util.regex.Pattern;

public abstract class LocationDescriptionTemplateMethod {
    public static final String START_OR_NOT_WORD_PATTERN = "^(?:.*\\W)?";
    public static final String END_OR_NOT_WORD_PATTERN = "(?:\\W.*)?$";
    private LocationService locationService;

    public LocationDescriptionTemplateMethod(LocationService locationService) {
        this.locationService = locationService;
    }

    public String createLocationDescription() {
        final Location location = getLocation();
        final String freeTextLocationName = getNotNullFreeTextLocationName();
        if (location == null) {
            return freeTextLocationName;
        } else {
            return getDescriptionFromFreeTextAndLocationData(location, freeTextLocationName);
        }
    }

    protected abstract String getFreeTextLocation();

    protected abstract Location getLocation();

    protected abstract Long getCategoryId();

    private String getNotNullFreeTextLocationName() {
        String freeTextLocation = getFreeTextLocation();
        return (freeTextLocation != null) ? freeTextLocation.trim() : "";
    }

    private String getDescriptionFromFreeTextAndLocationData(Location location, String freeTextLocationName) {
        String locationName = (freeTextLocationName.isEmpty()) ? location.getDisplayName() : freeTextLocationName;
        String landingLocationName = getLandingLocation(location).getDisplayName();
        if (containsLandingLocationName(locationName, landingLocationName)) {
            return locationName;
        } else {
            long categoryId = getNotNullCategoryId();
            return LocationDisplayUtils.getCleanedLocationText(locationName, landingLocationName, categoryId);
        }
    }

    private Location getLandingLocation(Location location) {
        if (location.isLanding()) {
            return location;
        } else {
            return locationService.getLanding(location);
        }
    }

    private long getNotNullCategoryId() {
        return (getCategoryId() != null) ? getCategoryId() : 0;
    }

    private boolean containsLandingLocationName(String locationName, String landingLocationName) {
        if (locationName.toLowerCase().contains(landingLocationName.toLowerCase())) {
            //the examination above is to not to create and match regex if it is not necessary
            return compilePatternForLocationContaining(landingLocationName).matcher(locationName).matches();
        } else {
            return false;
        }
    }

    private Pattern compilePatternForLocationContaining(String locationName) {
        return Pattern.compile(START_OR_NOT_WORD_PATTERN + locationName + END_OR_NOT_WORD_PATTERN);
    }
}
