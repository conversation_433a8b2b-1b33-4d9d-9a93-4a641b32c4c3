package com.gumtree.web.common.domain.ad;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

public class SalaryAttribute {
    private final Long gte;
    private final Long lte;

    public SalaryAttribute(Long gte, Long lte) {
        this.gte = gte;
        this.lte = lte;
    }

    public Long getGte() {
        return gte;
    }

    public Long getLte() {
        return lte;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        SalaryAttribute that = (SalaryAttribute) o;

        return new EqualsBuilder()
                .append(gte, that.gte)
                .append(lte, that.lte)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(gte)
                .append(lte)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "SalaryAttribute{" +
                "gte=" + gte +
                ", lte=" + lte +
                '}';
    }
}
