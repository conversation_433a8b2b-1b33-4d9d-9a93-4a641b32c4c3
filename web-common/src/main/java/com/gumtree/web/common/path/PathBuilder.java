package com.gumtree.web.common.path;

import com.gumtree.web.common.NameValuePair;
import com.gumtree.web.common.ViewUtils;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static com.gumtree.mobile.web.seo.SeoUtils.getSeoUrlFriendlyValueOf;

public final class PathBuilder {

    private List<String> pathSegments;
    private List<NameValuePair> queryParams;
    private Map<String, String> substitutions;

    private static final ViewUtils.Sanitiser SANITISER = new ViewUtils.Sanitiser('-');

    private PathBuilder(String basePath) {
        pathSegments = new ArrayList<>();
        queryParams = new ArrayList<>();
        substitutions = new HashMap<>();

        if (basePath != null) {
            String[] segments = basePath.split("/");
            for (String segment: segments) {
                addPathSegment(segment, false);
            }
        }
    }

    public static PathBuilder path() {
        return new PathBuilder(null);
    }

    public static PathBuilder path(String path) {
        return new PathBuilder(path);
    }

    public PathBuilder addSubstitution(String placeholder, String value) {
        Assert.notNull(placeholder);
        Assert.notNull(value);
        substitutions.put(placeholder, getSeoUrlFriendlyValueOf(value));
        return this;
    }

    public PathBuilder addPathSegment(String path) {
        addPathSegment(path, false);
        return this;
    }

    public PathBuilder addPathSegment(String path, boolean sanitise) {
        if (StringUtils.hasText(path)) {
            pathSegments.add(sanitise ? getSeoUrlFriendlyValueOf(path) : path);
        }
        return this;
    }

    public PathBuilder addQueryParam(String key, String value) {
        if (StringUtils.hasText(value)) {
            queryParams.add(new NameValuePair(key, value));
        }
        return this;
    }

    public String build() {
        StringBuilder builder = new StringBuilder();

        if (pathSegments.isEmpty()) {
            builder.append("/");
        }

        for (String pathSegment : pathSegments) {
            if (!pathSegment.startsWith("/")) {
                builder.append("/");
            }
            builder.append(pathSegment);
        }

        if (!queryParams.isEmpty()) {
            builder.append("?");
            Iterator<NameValuePair> it = queryParams.iterator();
            while (it.hasNext()) {
                NameValuePair current = it.next();
                builder.append(current.getName()).append("=").append(current.getValue());
                if (it.hasNext()) {
                    builder.append("&");
                }
            }
        }

        if (!substitutions.isEmpty()) {
            String path = builder.toString();
            for (Map.Entry<String, String> kw: substitutions.entrySet()) {
                path = path.replaceAll("\\{" + kw.getKey() + "\\}", kw.getValue());
            }
            return path.replaceAll("\\/uk\\?", "\\?");
        } else {
            return builder.toString().replaceAll("\\/uk\\?", "\\?");
        }
    }
}
