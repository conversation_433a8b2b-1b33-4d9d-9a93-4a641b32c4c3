package com.gumtree.web.common.domain.location;

import com.google.common.collect.Lists;
import com.gumtree.web.common.serializer.LocationSerializer;
import com.gumtree.web.common.serializer.LocationSerializerLegacy;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Please note that serializer removes several fields from the model, like zoomIns, which
 * may be necessary to use Locations in back end but which are not useful to FE
 */
@com.fasterxml.jackson.databind.annotation.JsonSerialize(using = LocationSerializer.class)
@JsonSerialize(using = LocationSerializerLegacy.class) // TODO fully migrate to fasterxml version(different package in web and seller)
public class Location {

    private static final String COUNTY_SEPARATOR = ",";

    private Long id;
    private String seoName;
    private String name;
    private String locality;
    private String region;
    private String country;
    private Location parent;
    private BigDecimal radius;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private List<Location> zoomIns = Lists.newArrayList();
    private boolean hasLandingPage;

    // is locality of this location unique?
    //   some locations have same locality but different county eq. Richmond in London or North Yorkshire
    private boolean localityUnique = true;

    /**
     * Note this should only be used for testing purposes, otherwise you risk errors due to mising data
     *
     * @param id      id of the location
     * @param seoName url compliant seo name
     * @param name    full name of the location
     */
    public Location(Long id,
                    String seoName,
                    String name) {
        this.id = id;
        this.seoName = seoName;
        setName(name);
    }

    public Location(Long id,
                    String seoName,
                    String name,
                    String country,
                    BigDecimal radius,
                    BigDecimal longitude,
                    BigDecimal latitude,
                    boolean landingPage) {
        this(id, seoName, name);
        this.country = country;
        this.radius = radius;
        this.longitude = longitude;
        this.latitude = latitude;
        this.hasLandingPage = landingPage;
    }

    public Long getId() {
        return id;
    }

    public String getSeoName() {
        return seoName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        this.locality = getLocalityName(name);
        this.region = getRegionName(name);
    }

    public Location getParent() {
        return parent;
    }

    public List<Location> getZoomIns() {
        return zoomIns;
    }

    public Location addZoomIn(Location zoomIn) {
        zoomIn.parent = this;
        zoomIns.add(zoomIn);
        return this;
    }

    public BigDecimal getRadius() {
        return radius;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public String getLocality() {
        return locality;
    }

    public String getRegion() {
        return region;
    }

    public String getCountry() {
        return country;
    }

    public boolean hasLandingPage() {
        return hasLandingPage;
    }

    public boolean isLocalityUnique() {
        return localityUnique;
    }

    public void setLocalityUnique(boolean localityUnique) {
        this.localityUnique = localityUnique;
    }

    @Override
    public String toString() {
        return "Location{" +
                "id=" + id +
                ", seoName='" + seoName + '\'' +
                ", name='" + name + '\'' +
                ", parent=" + parent +
                ", radius=" + radius +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                '}';
    }

    private String getLocalityName(String locationName) {
        String result = locationName;
        // if it has a , it contains the county, remove it
        if (locationName.contains(COUNTY_SEPARATOR)) {
            result = locationName.split(COUNTY_SEPARATOR)[0].trim();
        }
        return result;
    }

    private String getRegionName(String locationName) {
        String result = locationName;
        // if it has a , it contains the county, get it
        if (locationName.contains(COUNTY_SEPARATOR)) {
            result = locationName.split(COUNTY_SEPARATOR)[1].trim();
        }
        return result;
    }

    public GeoPointLocation getGeoPointLocation() {
        return new GeoPointLocation() {
            @Override
            public Optional<BigDecimal> getLatitude() {
                return Optional.ofNullable(latitude);
            }

            @Override
            public Optional<BigDecimal> getLongitude() {
                return Optional.ofNullable(longitude);
            }
        };
    }

    public boolean isZoomInOf(Location location) {
        Location current = this;
        while (current != null && !current.equals(location)) {
            current = current.parent;
        }
        return current != null && current.equals(location);
    }

    public boolean isRoot() {
        return "uk".equals(getSeoName());
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Location location = (Location) o;
        return (id != null && location.id != null && id.equals(location.id));
    }

    @Override
    public int hashCode() {
        return (id != null) ? id.hashCode() : 0;
    }
}
