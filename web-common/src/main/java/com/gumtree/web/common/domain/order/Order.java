package com.gumtree.web.common.domain.order;

import com.gumtree.web.common.domain.order.entity.OrderItemAd;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 */
public interface Order {
    /**
     * Get the order ID
     *
     * @return order ID
     */
    Long getId();

    /**
     * Get the account ID
     *
     * @return account ID
     */
    Long getAccountId();

    List<OrderItemAd> getAds();

    /**
     * Get the items associated with this order
     *
     * @return order items
     */
    List<OrderItem> getItems();

    /**
     * Get the total VAT for this order
     *
     * @return total VAT
     */
    BigDecimal getTotalVat();

    /**
     * Get the total price inclusive of VAT, for this order
     *
     * @return total price including VAT
     */
    BigDecimal getTotalIncVat();

    /**
     * Get the total price exclusive of VAT, for this order
     *
     * @return total price excluding VAT
     */
    BigDecimal getTotalExcVat();

    /**
     * Whether or not it's free
     *
     * @return is free?
     */
    Boolean isFree();
}
