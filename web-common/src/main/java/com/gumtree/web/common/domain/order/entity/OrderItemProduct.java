package com.gumtree.web.common.domain.order.entity;

import com.gumtree.seller.domain.payment.entity.PaymentMethod;
import com.gumtree.seller.domain.product.entity.ProductName;

import java.math.BigDecimal;

public class OrderItemProduct {

    private ProductName productName;
    private BigDecimal priceIncVat;
    private PaymentMethod paymentMethod;

    public OrderItemProduct(ProductName productName, BigDecimal priceIncVat, PaymentMethod paymentMethod) {
        this.productName = productName;
        this.priceIncVat = priceIncVat;
        this.paymentMethod = paymentMethod;
    }

    public ProductName getProductName() {
        return productName;
    }

    public BigDecimal getPriceIncVat() {
        return priceIncVat;
    }

    public PaymentMethod getPaymentMethod() {
        return paymentMethod;
    }
}
