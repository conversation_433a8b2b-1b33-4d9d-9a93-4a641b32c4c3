package com.gumtree.web.common.util;

import java.util.concurrent.ExecutorService;

/**
 * Factory for {@link ExecutorService}s
 */
public interface ExecutorServiceFactory {

    /**
     * Create a new fixed thread pool {@link ExecutorService}.
     *
     * @param nThreads the number of threads.
     * @return a new fixed thread pool {@link ExecutorService}.
     */
    ExecutorService newFixedThreadPool(int nThreads);
}
