package com.gumtree.web.common.path;

import com.gumtree.api.category.domain.Category;
import com.gumtree.common.util.url.UrlUtils;

import java.util.Optional;

import static com.gumtree.domain.location.Location.UK;
import static com.gumtree.web.common.path.PathBuilder.path;

public class BrowsePath extends AbstractCanonicalPath {

    private final Category category;
    private final String locationSeoName;

    public BrowsePath(Builder builder) {
        this.category = builder.category;
        this.locationSeoName = builder.locationSeoName;
    }

    public static BrowsePath.Builder builder() {
        return new Builder();
    }

    @Override
    protected String getPathSegment() {
        return null;
    }

    @Override
    public String getCanonicalPath() {
        PathBuilder builder = path(null);
        Optional<String> sanitisedLocation = encodeDecode(Optional.ofNullable(locationSeoName));

        addSearchCategory(builder, lowerCaseAndEncode(category.getSeoName()));
        addLocation(builder, sanitisedLocation.orElse(UK));
        return builder.build();
    }

    private void addSearchCategory(PathBuilder builder, String categorySeoName) {
        builder.addPathSegment(categorySeoName);
    }

    private void addLocation(PathBuilder builder, String sanitisedLocation) {
        if (!UK.equalsIgnoreCase(sanitisedLocation)) {
            builder.addPathSegment(sanitisedLocation);
        }
    }

    private static String lowerCaseAndEncode(String value) {
        return value != null ? encode(value.toLowerCase()) : null;
    }

    private static Optional<String> encodeDecode(Optional<String> input) {
        return input.map(val -> UrlUtils.enc(UrlUtils.dec(val.toLowerCase())));
    }

    private static String encode(String value) {
        String decoded = value;
        try {
            decoded = UrlUtils.dec(value);
        } catch (IllegalArgumentException e) {
            // handle: java.lang.IllegalArgumentException: URLDecoder: Incomplete trailing escape (%) pattern
            // os similar
            // ignore - as if value can't be decoded then it was not encoded at the first place so we can skip this step
        }
        return UrlUtils.enc(decoded);
    }

    public static class Builder {

        private Category category;
        private String locationSeoName;

        public Builder setCategory(Category category) {
            this.category = category;
            return this;
        }

        public BrowsePath build() {
            return new BrowsePath(this);
        }

        public Builder setLocation(String locationSeoName) {
            this.locationSeoName = locationSeoName;
            return this;
        }
    }
}
