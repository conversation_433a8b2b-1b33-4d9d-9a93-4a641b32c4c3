package com.gumtree.web.common.error;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * Implementation of {@link ErrorMessageResolver} that uses a {@link org.springframework.context.MessageSource}
 * to resolve messages.
 */
@Component
public final class MessageSourceErrorMessageResolver implements ErrorMessageResolver {

    private MessageSource messageSource;

    /**
     * Constructor.
     *
     * @param messageSource the message source
     */
    @Autowired
    public MessageSourceErrorMessageResolver(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    @Override
    public String getMessage(String code, String defaultMessage, Object... args) {

        String message = messageSource.getMessage(code, args, defaultMessage, Locale.getDefault());

        // TODO: This is an appalling dirty hack. Find another solution.
        if (message == null || message.equals(defaultMessage)) {
            message = messageSource.getMessage(defaultMessage, args, defaultMessage, Locale.getDefault());
        }

        return message != null ? message : code;
    }
}
