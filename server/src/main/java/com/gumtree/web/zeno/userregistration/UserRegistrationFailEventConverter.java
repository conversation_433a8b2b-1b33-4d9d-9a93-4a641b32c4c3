package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.converter.AbstractZenoEventConverter;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.Event;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationFail;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserRegistrationFailEventConverter extends AbstractZenoEventConverter<UserRegistrationFailZenoEvent, Event> {

    private RequestDetailsService requestDetailsService;

    @Autowired
    public UserRegistrationFailEventConverter(ZenoConverterService zenoConverterService,
                                              RequestDetailsService requestDetailsService) {
        super(UserRegistrationFailZenoEvent.class, zenoConverterService);

        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public Event convert(UserRegistrationFailZenoEvent userRegistrationBeginZenoEvent) {

        PageData pageData = requestDetailsService.getPageData(PageType.UserRegistrationForm);
        UserData userData = requestDetailsService.getUserData();
        DeviceData deviceData = requestDetailsService.getDeviceData();

        return new UserRegistrationFail(pageData, userData, deviceData);
    }
}
