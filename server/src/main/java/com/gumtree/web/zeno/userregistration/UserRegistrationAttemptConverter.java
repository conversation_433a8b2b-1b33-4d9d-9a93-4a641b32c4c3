package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.converter.AbstractEventConverter;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationAttempt;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserRegistrationAttemptConverter extends AbstractEventConverter<String[], String, UserRegistrationAttempt> {

    private RequestDetailsService requestDetailsService;

    @Autowired
    public UserRegistrationAttemptConverter(ZenoConverterService zenoConverterService,
                                            RequestDetailsService requestDetailsService) {
        super(UserRegistrationAttempt.class, zenoConverterService);
        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public UserRegistrationAttempt convertToEvent(String[] input, String output) {
        PageData pageData = requestDetailsService.getPageData(PageType.UserRegistrationForm);
        UserData userData = requestDetailsService.getUserData();
        DeviceData deviceData = requestDetailsService.getDeviceData();

        return new UserRegistrationAttempt(pageData, userData, deviceData);
    }

}
