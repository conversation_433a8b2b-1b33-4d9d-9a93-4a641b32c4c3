package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.converter.AbstractZenoEventConverter;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.Event;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationSuccess;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserRegistrationSuccessEventConverter extends AbstractZenoEventConverter<UserRegistrationSuccessZenoEvent, Event> {

    private RequestDetailsService requestDetailsService;

    @Autowired
    public UserRegistrationSuccessEventConverter(ZenoConverterService zenoConverterService,
                                                 RequestDetailsService requestDetailsService) {
        super(UserRegistrationSuccessZenoEvent.class, zenoConverterService);

        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public Event convert(UserRegistrationSuccessZenoEvent userRegistrationSuccessZenoEvent) {

        PageData pageData = requestDetailsService.getPageData(PageType.UserRegistrationSuccess);
        // as the user isn't logged in, requestDetailsService.getUserData() is empty
        UserData userData = UserData.aUser()
                .withLoggedIn(false)
                .withLoginFailure(false)
                .withUserId(userRegistrationSuccessZenoEvent.getUserId())
                .withUserEmail(userRegistrationSuccessZenoEvent.getEmailAddress())
                .withAccountType(UserData.AccountType.Standard)
                .build();

        DeviceData deviceData = requestDetailsService.getDeviceData();

        return new UserRegistrationSuccess(pageData, userData, deviceData);
    }
}
