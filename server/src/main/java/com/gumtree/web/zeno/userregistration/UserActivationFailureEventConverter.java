package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.converter.AbstractZenoEventConverter;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.Event;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserActivationFail;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserActivationFailureEventConverter extends AbstractZenoEventConverter<UserActivationFailureZenoEvent, Event> {

    private RequestDetailsService requestDetailsService;

    @Autowired
    public UserActivationFailureEventConverter(ZenoConverterService zenoConverterService,
                                               RequestDetailsService requestDetailsService) {
        super(UserActivationFailureZenoEvent.class, zenoConverterService);

        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public Event convert(UserActivationFailureZenoEvent userActivationFailureZenoEvent) {

        PageData pageData = requestDetailsService.getPageData(PageType.UserActivationFail);
        // as the user isn't logged in, requestDetailsService.getUserData() is empty
        UserData userData = UserData.aUser()
                .withLoggedIn(false)
                .withLoginFailure(false)
                .withUserEmail(userActivationFailureZenoEvent.getEmailAddress())
                .withAccountType(UserData.AccountType.Standard)
                .build();

        DeviceData deviceData = requestDetailsService.getDeviceData();

        return new UserActivationFail(pageData, userData, deviceData);
    }
}
