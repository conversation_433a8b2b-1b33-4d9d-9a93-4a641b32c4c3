package com.gumtree.web.zeno.userregistration;

import com.gumtree.web.seller.page.activation.model.EmailVerificationModel;
import com.gumtree.zeno.core.converter.AbstractEventConverter;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.verification.EmailVerification;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class EmailVerificationConverter extends AbstractEventConverter<PageType, EmailVerificationModel, EmailVerification> {
    private RequestDetailsService requestDetailsService;

    @Autowired
    public EmailVerificationConverter(ZenoConverterService zenoConverterService,
                                      RequestDetailsService requestDetailsService) {
        super(EmailVerification.class, zenoConverterService);
        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public EmailVerification convertToEvent(PageType in, EmailVerificationModel out) {
        PageData pageData = requestDetailsService.getPageData(in);
        UserData userData = requestDetailsService.getUserData();
        DeviceData deviceData = requestDetailsService.getDeviceData();

        return new EmailVerification(pageData, userData, deviceData, out.isVerified());
    }
}
