package com.gumtree.web.zeno.userregistration;

import com.google.common.base.Objects;
import com.gumtree.zeno.core.event.ZenoEvent;

public class UserRegistrationSuccessZenoEvent implements ZenoEvent {

    private Long userId;
    private String emailAddress;

    public UserRegistrationSuccessZenoEvent(Long userId, String emailAddress) {
        this.userId = userId;
        this.emailAddress = emailAddress;
    }

    public Long getUserId() {
        return userId;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserRegistrationSuccessZenoEvent that = (UserRegistrationSuccessZenoEvent) o;
        return Objects.equal(userId, that.userId) &&
                Objects.equal(emailAddress, that.emailAddress);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(userId, emailAddress);
    }
}
