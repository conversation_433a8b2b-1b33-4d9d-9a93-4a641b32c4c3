package com.gumtree.web.zeno.converter.postad;

import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.zeno.HierarchyUtil;
import com.gumtree.zeno.core.converter.AbstractEventConverter;
import com.gumtree.zeno.core.domain.AdvertData;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.HierarchicalData;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.postad.AbstractPostAdEvent;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractPostAdEventConverter<A extends AbstractPostAdEvent>
        extends AbstractEventConverter<AdvertEditor, Void, A> {

    protected RequestDetailsService requestDetailsService;
    private HierarchyUtil hierarchyUtil;

    @Autowired
    public AbstractPostAdEventConverter(Class<? extends A> eventClass, ZenoConverterService zenoConverterService,
                                RequestDetailsService requestDetailsService, HierarchyUtil hierarchyUtil) {
        super(eventClass, zenoConverterService);
        this.requestDetailsService = requestDetailsService;
        this.hierarchyUtil = hierarchyUtil;
    }

    @Override
    public A convertToEvent(AdvertEditor advertEditor, Void ignore) {
        UserData userData = requestDetailsService.getUserData();
        DeviceData deviceData = requestDetailsService.getDeviceData();
        HierarchicalData categoryData = hierarchyUtil.forCategory(advertEditor.getCategoryId());
        Long locationId = advertEditor.getLocationId();
        String postcode = advertEditor.getPostcode();
        HierarchicalData locationData = hierarchyUtil.forLocation(locationId, postcode, null, null, null);
        AdvertData advertData = new AdvertData.Builder()
                .withId(advertEditor.getAdvertId())
                .build();

        return createEvent(userData, deviceData, categoryData, locationData, advertData);

    }

    protected abstract A createEvent(UserData userData, DeviceData deviceData, HierarchicalData categoryData,
                                     HierarchicalData locationData, AdvertData advertData);

}
