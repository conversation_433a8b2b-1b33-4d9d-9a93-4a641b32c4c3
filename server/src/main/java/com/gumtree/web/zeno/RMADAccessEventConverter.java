package com.gumtree.web.zeno;

import com.gumtree.zeno.core.domain.ManageAdsData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.RMADAccess;
import com.gumtree.zeno.core.converter.AbstractEventConverter;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * Converter for Zeno logging of access to RMAD page
 * User: pvillega
 */
@Component
public class RMADAccessEventConverter extends AbstractEventConverter<Object[], String, RMADAccess> {

    private RequestDetailsService requestDetailsService;

    @Autowired
    public RMADAccessEventConverter(ZenoConverterService zenoConverterService,
                                    RequestDetailsService requestDetailsService) {
        super(RMADAccess.class, zenoConverterService);
        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public RMADAccess convertToEvent(Object[] input, String output) {
        Assert.isInstanceOf(String.class, input[0]);
        Assert.isInstanceOf(String.class, input[1]);
        String override = (String) input[0];
        String responsiveGroup = (String) input[1];

        PageData pageData = requestDetailsService.getPageData(PageType.MyAds);
        UserData userData = requestDetailsService.getUserData();
        ManageAdsData manageAdsData = ManageAdsData.aManageAds()
                .withOverride(override)
                .withResponsiveGroup(responsiveGroup)
                .build();

        return new RMADAccess(pageData, userData, requestDetailsService.getDeviceData(), manageAdsData);
    }

}
