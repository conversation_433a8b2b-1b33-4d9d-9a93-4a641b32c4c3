package com.gumtree.web.seller.page.postad.reporting.ga.events;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsNamedTrackEvent;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsNamedTrackEventName;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsTrackEventAction;
import com.gumtree.web.reporting.google.ga.impl.GoogleAnalyticsTrackEventLabelBuilder;

public class PostAdPreview extends GoogleAnalyticsNamedTrackEvent {

    public PostAdPreview(ThirdPartyRequestContext<?> ctx) {
        super(GoogleAnalyticsNamedTrackEventName.POST_AD_PREVIEW, ctx.getPageType(),
                GoogleAnalyticsTrackEventAction.POST_AD_PREVIEW);
//        setLabel(eventLabel(ctx));
    }

    private String eventLabel(ThirdPartyRequestContext<?> ctx) {
        return new GoogleAnalyticsTrackEventLabelBuilder()
                .catId(ctx.getCategory())
                .locId(ctx.getLocation())
                .build();
    }


}


