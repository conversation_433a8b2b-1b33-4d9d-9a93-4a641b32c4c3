package com.gumtree.web.seller.page.redirect.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.BaseSellerController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.view.RedirectView;

/**
 * Controller that handles legacy URLs that we still need to support post-migration. These are the URLs
 * that we have decided are too important to allow to die completely, so we redirect them to the appropriate
 * place in Bushfire.
 */
@Controller
public final class PostMigrationLegacyRedirectController extends BaseSellerController {

    @Autowired
    public PostMigrationLegacyRedirectController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                                 ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                                 UrlScheme urlScheme, UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
    }

    /**
     * Always redirect legacy login URLs to the Bushfire login page.
     *
     * @return redirect url
     */
    @RequestMapping(value = {"/account/login", "/proaccount/login"},
            method = {RequestMethod.GET, RequestMethod.POST})
    public RedirectView handleLegacyLogin() {
        RedirectView redirectView = new RedirectView(getUrlScheme().urlFor(Actions.BUSHFIRE_LOGIN), false, true, false);
        redirectView.setStatusCode(HttpStatus.MOVED_PERMANENTLY);
        return redirectView;
    }
}
