package com.gumtree.web.seller.page.password.controller;

import com.gumtree.web.seller.page.common.model.Form;
import org.hibernate.validator.constraints.Email;

public final class ResetPasswordFormBean extends Form {

    @Email(message = "reset.password.username.invalid")
    private String username;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
}
