package com.gumtree.web.seller.page.adstats.controller;

import com.gumtree.web.seller.model.AdPreview;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.service.adstats.AdvertStatisticData;
import org.springframework.web.servlet.ModelAndView;

public final class ManageAdStatsModel extends CommonModel {

    private final AdvertStatisticData adstats;

    private final AdPreview advert;

    private ManageAdStatsModel(CoreModel core, Builder builder) {
        super(core);
        this.adstats = builder.adStats;
        this.advert = builder.advert;
    }

    public AdvertStatisticData getAdstats() {
        return adstats;
    }

    public AdPreview getAdvert() {
        return advert;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private AdvertStatisticData adStats;

        private AdPreview advert;

        public Builder withAdStats(AdvertStatisticData adStats) {
            this.adStats = adStats;
            return this;
        }

        public Builder withAdvert(AdPreview advert) {
            this.advert = advert;
            return this;
        }

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page = Page.StatsAd;
            coreModelBuilder.withTitle("Log out"); //FIXME
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new ManageAdStatsModel(coreModelBuilder.build(page), this));
        }

    }
}
