package com.gumtree.web.seller.page.reviews.model;

import java.util.HashMap;
import java.util.Map;

import static com.gumtree.web.seller.page.reviews.service.UserReviewsService.formatAvgRating;

public class UserRating {
    public static final int RATING_SCALE = 5;
    private final Integer total;
    private final Float average;
    private final Map<Integer, Integer> breakdown;


    public UserRating() {
        this.total = 0;
        this.average = 0f;
        this.breakdown = new HashMap<>();
        for (int i = 1; i <= RATING_SCALE; i++) {
            breakdown.put(i, 0);
        }
    }

    public UserRating(Integer total, Float average, Map<Integer, Integer> breakdown) {
        this.total = total;
        this.average = average;
        this.breakdown = breakdown;
    }

    public Integer getTotal() {
        return total;
    }

    public Float getAverage() {
        return average;
    }

    public String getFormattedAverage() {
        return formatAvgRating(average);
    }

    public Map<Integer, Integer> getBreakdown() {
        return breakdown;
    }

    @Override
    public String toString() {
        return "UserRating{" +
                "total=" + total +
                ", average='" + average + '\'' +
                ", breakdown=" + breakdown +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof UserRating)) return false;

        UserRating that = (UserRating) o;

        if (total != null ? !total.equals(that.total) : that.total != null) return false;
        if (average != null ? !average.equals(that.average) : that.average != null) return false;
        return breakdown != null ? breakdown.equals(that.breakdown) : that.breakdown == null;
    }

    @Override
    public int hashCode() {
        int result = total != null ? total.hashCode() : 0;
        result = 31 * result + (average != null ? average.hashCode() : 0);
        result = 31 * result + (breakdown != null ? breakdown.hashCode() : 0);
        return result;
    }
}
