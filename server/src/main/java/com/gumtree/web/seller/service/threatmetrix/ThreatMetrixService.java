package com.gumtree.web.seller.service.threatmetrix;

import com.gumtree.common.properties.GtProps;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.CookieUtils;
import com.gumtree.web.cookie.GumtreeCookieProperty;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookieCutter;
import com.gumtree.web.reporting.threatmetrix.ThreatMetrixTracking;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

/**
 * 统一的ThreatMetrix服务，处理Cookie生成和Tracking数据创建
 * 保持与现有架构的一致性，同时支持API响应场景
 */
@Service
public class ThreatMetrixService {

    private static final Logger log = LoggerFactory.getLogger(ThreatMetrixService.class);

    @Autowired
    private CookieResolver cookieResolver;

    @Autowired
    private ThreatMetrixCookieCutter threatMetrixCookieCutter;

    @Value("${gumtree.threatmetrix.orgId:njrya493}")
    private String organisationId;

    @Value("${gumtree.threatmetrix.webBaseUrl}")
    private String webBaseUrl;

    @Value("${gumtree.threatmetrix.enabled:false}")
    private boolean enabled;

    /**
     * 为API响应处理ThreatMetrix Cookie和Tracking
     * 这个方法遵循现有的Cookie处理机制，确保与其他场景的一致性
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @return ThreatMetrixInfo 包含Cookie和Tracking信息
     */
    public ThreatMetrixInfo processThreatMetrixForApiResponse(HttpServletRequest request, HttpServletResponse response) {
        if (!enabled) {
            return ThreatMetrixInfo.disabled();
        }

        // 使用统一的CookieResolver获取或创建ThreatMetrix Cookie
        // 这确保了与现有架构的一致性
        ThreatMetrixCookie tmCookie = cookieResolver.resolve(request, ThreatMetrixCookie.class);
        
        // 创建ThreatMetrix追踪数据
        ThreatMetrixTracking tracking = ThreatMetrixTracking.builder()
                .orgId(organisationId)
                .sessionId(tmCookie.getDefaultValue())
                .webBaseUrl(webBaseUrl)
                .build();

        // 手动设置Cookie到响应中（因为没有ModelAndView，CookieHandlerInterceptor不会自动处理）
        // 使用与CookieHandlerInterceptor完全相同的逻辑，包括环境相关的Cookie命名
        String environmentAwareCookieName = threatMetrixCookieCutter.getName(GtProps.getEnv());
        Cookie cookie = CookieUtils.createHttpCookie(environmentAwareCookieName, tmCookie);
        response.addCookie(cookie);

        return new ThreatMetrixInfo(tmCookie, tracking, true);
    }

    /**
     * 获取现有的ThreatMetrix Cookie（如果存在）
     * 用于其他需要读取ThreatMetrix信息的场景
     *
     * @param request HTTP请求
     * @return ThreatMetrixCookie 或 null（如果不存在）
     */
    public ThreatMetrixCookie getExistingThreatMetrixCookie(HttpServletRequest request) {
        try {
            return cookieResolver.resolve(request, ThreatMetrixCookie.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 使ThreatMetrix Cookie过期
     * 这个方法会正确地将过期的Cookie发送到浏览器，确保Cookie被删除
     *
     * 关键修复：采用多重删除策略，同时处理 host-only cookie 和 domain cookie，
     * 避免因 domain 格式不匹配导致的 Cookie 删除失败问题
     *
     * 增强功能：
     * 1. 多重domain格式删除
     * 2. 添加防缓存响应头
     * 3. 强制过期时间设置
     *
     * @param request HTTP请求
     * @param response HTTP响应
     */
    public void invalidateThreatMetrixCookie(HttpServletRequest request, HttpServletResponse response) {
        try {
            if (!enabled) {
                return;
            }

            String environmentAwareCookieName = threatMetrixCookieCutter.getName(GtProps.getEnv());
            Optional<Cookie> existingCookie =
                    CookieUtils.findCookie(request.getCookies(), environmentAwareCookieName);

            if (existingCookie.isPresent()) {
                Cookie originalCookie = existingCookie.get();
                originalCookie.setMaxAge(0);
                originalCookie.setValue("");
                originalCookie.setDomain(GtProps.getStr(GumtreeCookieProperty.COOKIES_DOMAIN));
                originalCookie.setSecure(GtProps.getBool(GumtreeCookieProperty.COOKIES_SECURE));
                originalCookie.setPath(ThreatMetrixCookieCutter.PATH);
                response.addCookie(originalCookie);
            }
        } catch (Exception e) {
            log.error("Error invalidating ThreatMetrix Cookie", e);
        }
    }

    /**
     * 检查ThreatMetrix是否启用
     * 
     * @return boolean 是否启用
     */
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * 获取组织ID
     * 
     * @return String 组织ID
     */
    public String getOrganisationId() {
        return organisationId;
    }

    /**
     * 获取Web基础URL
     * 
     * @return String Web基础URL
     */
    public String getWebBaseUrl() {
        return webBaseUrl;
    }
}
