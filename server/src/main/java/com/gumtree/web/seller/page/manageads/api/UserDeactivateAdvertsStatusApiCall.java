package com.gumtree.web.seller.page.manageads.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.api.domain.advert.NewStatusBean;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;

/**
 * Comment
 */
public class UserDeactivateAdvertsStatusApiCall extends AuthenticatedApiCall<Void> {

    private NewStatusBean newStatusBean;

    private Long accountId;

    /**
     * Constructor
     *
     * @param accountId      - account id
     * @param newStatusBean - status bean
     * @param apiKeyProvider        - user's api key
     */
    public UserDeactivateAdvertsStatusApiCall(Long accountId, NewStatusBean newStatusBean,
                                              ApiKeyProvider apiKeyProvider) {
        super(apiKeyProvider);
        this.accountId = accountId;
        this.newStatusBean = newStatusBean;
    }

    @Override
    public final Void execute(BushfireApi api) {
        api.create(AccountApi.class, getApiKey()).newAdvertStatuses(getAccountId(), getNewStatusBean());
        return null;
    }

    public final NewStatusBean getNewStatusBean() {
        return newStatusBean;
    }

    public final Long getAccountId() {
        return accountId;
    }
}