package com.gumtree.web.seller.page.postad.model.path;

import com.gumtree.web.common.path.AbstractPath;

import static com.gumtree.web.common.path.PathBuilder.path;

public class PostAdBumpupPath  extends AbstractPath {

    private String editorId;

    private static final String PREFIX = "postad";
    private static final String SUFFIX = "bumpup";

    public static final String MAPPING = "/postad/{editorId}/bumpup";

    public PostAdBumpupPath(String editorId) {
        this.editorId = editorId;
    }

    @Override
    public String getPathSegment() {
        return path()
                .addPathSegment(PREFIX)
                .addPathSegment(editorId)
                .addPathSegment(SUFFIX)
                .build();
    }
}
