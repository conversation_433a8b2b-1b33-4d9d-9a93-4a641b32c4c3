package com.gumtree.web.seller.page.registration;

import com.google.common.collect.Lists;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.domain.user.beans.RegisterUserMethod;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.properties.utils.PropSupplier;
import com.gumtree.config.SellerProperty;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.AuthenticationProvider;
import com.gumtree.user.service.model.RegisteredUser;
import com.gumtree.user.service.model.UserRegistrationRequest;
import com.gumtree.user.service.support.builder.UserRegistrationRequestBuilder;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;
import com.gumtree.web.common.ip.RemoteIP;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.reporting.threatmetrix.ThreatMetrix;
import com.gumtree.web.security.exception.FormValidationException;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.registration.model.RegistrationForm;
import com.gumtree.web.seller.page.registration.model.RegistrationModel;
import com.gumtree.web.seller.page.registration.model.RegistrationResult;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.gumtree.web.seller.service.threatmetrix.ThreatMetrixService;
import com.gumtree.web.seller.service.user.forgotpassword.PasswordResetService;
import com.gumtree.web.zeno.userregistration.UserRegistrationBeginZenoEvent;
import com.gumtree.web.zeno.userregistration.UserRegistrationFailZenoEvent;
import com.gumtree.web.zeno.userregistration.UserRegistrationSuccessZenoEvent;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationBegin;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationFail;
import com.gumtree.zeno.core.service.ZenoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;

/**
 * Controller for handling user registration.
 */
@Controller
@RequestMapping(RegistrationPageController.PAGE_PATH)
@GoogleAnalytics
public final class RegistrationPageController extends BaseSellerController {

    public static final String PAGE_PATH = "/create-account";
    private static final String ENCRYPTED_MAP_PARAMETER_NAME = "gt_d";
    private static final String REGISTRATION_FAILURE = "Registration Failure";
    private static final String EMAIL_EXISTS_ERROR_MESSAGE = "registerUser.email.exists";
    private static final String EMAIL_EXISTS_ERROR_MESSAGE_NEW = "registerUser.email.exists.new";
    private static final String FIELD_TO_REPLACE_EMAIL_ADDRESS = "username";
    public static final String EXPERIMENT_REGISTRATION_PATH = "/new-registration";

    private LoginUtils loginUtils;
    private UserServiceFacade userServiceFacade;
    private ZenoService zenoService;
    private PropSupplier<String> facebookAppId = GtProps.getDStr(SellerProperty.FACEBOOK_APP_ID);
    private PropSupplier<String> googleAppId = GtProps.getDStr(SellerProperty.GOOGLE_APP_ID);
    private ParameterEncryption parameterEncryption;
    private PasswordResetService passwordResetService;
    private ThreatMetrixService threatMetrixService;

    @Autowired
    public RegistrationPageController(CookieResolver cookieResolver,
                                      CategoryModel categoryModel,
                                      ApiCallExecutor apiCallExecutor,
                                      ErrorMessageResolver messageResolver,
                                      UrlScheme urlScheme,
                                      LoginUtils loginUtils,
                                      ZenoService zenoService,
                                      UserSessionService userSessionService,
                                      UserServiceFacade userServiceFacade,
                                      PasswordResetService passwordResetService,
                                      ParameterEncryption parameterEncryption,
                                      ThreatMetrixService threatMetrixService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.loginUtils = loginUtils;
        this.zenoService = zenoService;
        this.userServiceFacade = userServiceFacade;
        this.passwordResetService = passwordResetService;
        this.parameterEncryption = parameterEncryption;
        this.threatMetrixService = threatMetrixService;
    }

    @GumtreePage(PageType.UserRegistrationForm)
    @RequestMapping(method = RequestMethod.GET)
    @ThreatMetrix
    public ModelAndView viewRegistrationPage(@ModelAttribute("registerUserBean") RegistrationForm registrationForm,
                                             @RequestParam(value = "email", required = false) String email,
                                             HttpServletRequest request) {

        zenoService.logEvent(new UserRegistrationBeginZenoEvent(email));

        // default bean that sets the opt in for News, Offers to True by default
        registrationForm.setOptInMarketing(true);
        if (email == null) {
            email = loginUtils.getNewUserEmailAddressFromSession();
        }
        registrationForm.setEmailAddress(email);
        registrationForm.setRegisterUserMethod(RegisterUserMethod.REGISTER_FLOW);

        loginUtils.clearNewUserEmailAddressFromSession();
        loginUtils.setNewUserMustLoginToStartPostAdFlow(true);

        CoreModel.Builder coreModel = getCoreModelBuilder(request)
                .withGaEvents(Lists.newArrayList(UserRegistrationBegin.class.getSimpleName()));
        RegistrationModel.Builder registrationModel = RegistrationModel.builder()
                .withRegistrationForm(registrationForm)
                .withFacebookAppId(facebookAppId.get())
                .withGoogleAppId(googleAppId.get());

        return registrationModel.build(coreModel);
    }

    /**
     * Default action
     *
     * @return register page
     */
    @ModelAttribute("registerFormAction")
    public String registerFormAction() {
        return PAGE_PATH;
    }

    @GumtreePage(PageType.UserRegistrationForm)
    @RequestMapping(method = RequestMethod.POST)
    public ModelAndView registerUser(
            @ModelAttribute("model") RegistrationModel incomingModel,
            HttpServletRequest request,
            RemoteIP remoteIP) {

        RegistrationForm registrationForm = incomingModel.getForm();

        ThreatMetrixCookie tmCookie = cookieResolver.resolve(request, ThreatMetrixCookie.class);
        UserRegistrationRequest regRequest = convertToRegistrationRequest(registrationForm, tmCookie, remoteIP);
        ApiResponse<RegisteredUser> resp = userServiceFacade.registerUser(regRequest);

        if (!resp.isDefined()) {
            return registrationFormWithErrors(request, registrationForm, resp);
        }

        if (resp.isDefined()) {
            tmCookie.invalidate();
        }

        return redirectToConfirmation(request, registrationForm, resp);
    }

    @GumtreePage(PageType.UserRegistrationForm)
    @RequestMapping(value = EXPERIMENT_REGISTRATION_PATH, method = RequestMethod.POST)
    public ResponseEntity<RegistrationResult> registerNewUser(
            @ModelAttribute("model") RegistrationModel incomingModel,
            HttpServletRequest request,
            RemoteIP remoteIP,
            GumtreePageContext gumtreePageContext) {

        CoreModel.Builder coreModelBuilder = getCoreModelBuilder(request);

        RegistrationForm registrationForm = incomingModel.getForm();

        ThreatMetrixCookie tmCookie = cookieResolver.resolve(request, ThreatMetrixCookie.class);

        UserRegistrationRequest regRequest = convertToRegistrationRequest(registrationForm, tmCookie, remoteIP);
        ApiResponse<RegisteredUser> resp = userServiceFacade.registerUser(regRequest);

        if (!resp.isDefined()) {
            RegistrationForm registrationFormWithErrors = populateError(registrationForm, resp);
            coreModelBuilder.withGaEvents(Lists.newArrayList(UserRegistrationFail.class.getSimpleName()));

            return new ResponseEntity<>(
                    buildRegistrationResult(true, registrationFormWithErrors.getFormErrors(),
                            null, coreModelBuilder, Page.Registration),
                    HttpStatus.BAD_REQUEST
            );
        }

        invalidateThreatmetrixSession(gumtreePageContext);
        Long userId = resp.get().getUserId();
        populateGumtreeContext(gumtreePageContext, registrationForm.getEmailAddress(), userId);
        sendZenoRegisteredEvent(gumtreePageContext);

        String resendPath = ResendActivationEmailPageController.EXPERIMENT_PAGE_PATH + "/" + userId;

        return new ResponseEntity<>(
                buildRegistrationResult(false, null,
                resendPath, coreModelBuilder, Page.RegistrationConfirmation),
                HttpStatus.OK
        );
    }

    private void invalidateThreatmetrixSession(GumtreePageContext context) {
        HttpServletRequest httpServletRequest = context.getHttpServletRequest();
        HttpServletResponse httpServletResponse = context.getHttpServletResponse();

        threatMetrixService.invalidateThreatMetrixCookie(httpServletRequest, httpServletResponse);
    }

    private RegistrationResult buildRegistrationResult(boolean withError,
                                                       Map<String, List<String>> errors,
                                                       String resendPath,
                                                       CoreModel.Builder coreModelBuilder,
                                                       Page page){
        return RegistrationResult.builder()
                .withError(withError)
                .errors(errors)
                .resendPath(resendPath)
                .build(coreModelBuilder, page);
    }

    private void sendZenoRegisteredEvent(GumtreePageContext gumtreePageContext) {
        User user = gumtreePageContext.getUser();
        zenoService.logEvent(new UserRegistrationSuccessZenoEvent(user.getId(), user.getEmail()));
    }

    /**
     * Populate the gumtreePageContext with anything that is required from 3rd party services
     *
     * @param gumtreePageContext the page context
     * @param emailAddress email address
     * @param userId user id
     */
    private void populateGumtreeContext(GumtreePageContext gumtreePageContext, String emailAddress, Long userId) {
        User user = new User();
        user.setEmail(emailAddress);
        user.setId(userId);
        gumtreePageContext.setUser(user);
    }

    private RegistrationForm populateError(RegistrationForm registrationForm, ApiResponse<RegisteredUser> resp){
        if (!registrationForm.getLegacy()) {
            setErrorsForNewRegistrationVersionWhenEmailExists(resp);
        }
        ReportableErrorsMessageResolvingErrorSource reportableErrors = new ReportableErrorsMessageResolvingErrorSource(
                new ReportableUserApiErrors(resp.getError()),
                messageResolver
        );
        registrationForm.addErrors(reportableErrors);
        registrationForm.clearPasswordFields();
        zenoService.logEvent(new UserRegistrationFailZenoEvent(registrationForm.getEmailAddress()));

        return registrationForm;
    }

    private ModelAndView registrationFormWithErrors(HttpServletRequest request,
                                                    RegistrationForm registrationForm,
                                                    ApiResponse<RegisteredUser> resp) {
        CoreModel.Builder coreModel = getCoreModelBuilder(request);
        RegistrationModel.Builder registrationModel = RegistrationModel.builder()
                .withRegistrationForm(registrationForm);

        if (!registrationForm.getLegacy()) {
            setErrorsForNewRegistrationVersionWhenEmailExists(resp);
        }

        ReportableErrorsMessageResolvingErrorSource reportableErrors = new ReportableErrorsMessageResolvingErrorSource(
                new ReportableUserApiErrors(resp.getError()),
                messageResolver
        );
        registrationForm.addErrors(reportableErrors);
        registrationForm.clearPasswordFields();
        coreModel.withGaEvents(Lists.newArrayList(UserRegistrationFail.class.getSimpleName()));

        zenoService.logEvent(new UserRegistrationFailZenoEvent(registrationForm.getEmailAddress()));
        if (!registrationForm.getLegacy()) {
            throw new FormValidationException(REGISTRATION_FAILURE, registrationForm.getFormErrors());
        }

        return registrationModel.build(coreModel);
    }

    private ModelAndView redirectToConfirmation(HttpServletRequest request,
                                                RegistrationForm registrationForm,
                                                ApiResponse<RegisteredUser> resp) {
        Long userId = resp.isDefined() ? resp.get().getUserId() : -1L;

        Map<String, String> redirectParams = new HashMap<>();
        redirectParams.put("emailAddress", registrationForm.getEmailAddress());
        redirectParams.put("userId", userId.toString());
        redirectParams.put("registrationFlow", "true");
        String encryptedParameterMap = parameterEncryption.encryptMapAndUrlEncode(redirectParams);

        ModelAndView redirect = redirectWithParameters(getRedirectUrl(request), ENCRYPTED_MAP_PARAMETER_NAME, encryptedParameterMap);

        return redirect;
    }

    private String getRedirectUrl(HttpServletRequest request) {
        return isRedirectForIOS(request) ? ConfirmationPageController.PAGE_PATH + "?result=success"
                : ConfirmationPageController.PAGE_PATH;
    }

    private boolean isRedirectForIOS(HttpServletRequest request) {
        return request.getQueryString() != null;
    }

    static UserRegistrationRequest convertToRegistrationRequest(RegistrationForm form, ThreatMetrixCookie tmCookie, RemoteIP remoteIP) {
        UserRegistrationRequest registrationRequest = UserRegistrationRequestBuilder.builder()
                .setUsername(form.getEmailAddress())
                .setAccessToken(form.getPassword())
                .setFirstName(form.getFirstName())
                .setLastName(form.getLastName())
                .setOptInMarketing(form.isOptInMarketing())
                .setPhoneNumber(form.getTelephoneNumber())
                .setAuthenticationProvider(AuthenticationProvider.GUMTREE)
                .setOptInThirdPartyMarketing(form.getOptInThirdPartyMarketing())
                .setThreatMetrixSessionId(tmCookie.getDefaultValue())
                .setIpAddress(remoteIP.getIpAddress())
                .setDateOfBirth(form.getDateOfBirth() != null ? LocalDate.parse(form.getDateOfBirth(), DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null)
                .setPostcode(form.getPostcode())
                .build();

        return registrationRequest;
    }

    private void setErrorsForNewRegistrationVersionWhenEmailExists(ApiResponse<RegisteredUser> resp) {
        ofNullable(resp)
                .map(ApiResponse::getError)
                .flatMap(errors -> ofNullable(errors.getErrors()))
                .ifPresent(errors -> errors.stream()
                        .filter(error -> nonNull(error.getError())
                                && error.getError().equalsIgnoreCase(EMAIL_EXISTS_ERROR_MESSAGE))
                        .forEach(error -> {
                            error.setField(FIELD_TO_REPLACE_EMAIL_ADDRESS);
                            error.setError(EMAIL_EXISTS_ERROR_MESSAGE_NEW);
                        }));
    }
}
