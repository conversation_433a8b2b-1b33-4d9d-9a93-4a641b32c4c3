package com.gumtree.web.seller.page.postad.model;

import com.gumtree.common.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class PostAdAttribute {
    private boolean mandatory;
    private PostAdAttributeType type;
    private String label;
    private Boolean displayLabel;
    private List<PostAdAttributeValue> values = new ArrayList<>();
    private String id;
    private String description;
    private Boolean priceSensitive;

    public final String getId() {
        return id;
    }

    public final void setId(String id) {
        this.id = id;
    }

    public final String getLabel() {
        return label;
    }

    public final boolean getPriceSensitive() {
        return priceSensitive;
    }

    public final void setLabel(String label) {
        this.label = label;
    }

    public Boolean isDisplayLabel() {
        return displayLabel;
    }

    public void setDisplayLabel(Boolean displayLabel) {
        this.displayLabel = displayLabel;
    }

    public final boolean isMandatory() {
        return mandatory;
    }

    public final void setMandatory(boolean mandatory) {
        this.mandatory = mandatory;
    }

    public final PostAdAttributeType getType() {
        return type;
    }

    public final void setType(PostAdAttributeType type) {
        this.type = type;
    }

    public final List<PostAdAttributeValue> getValues() {
        return values;
    }

    public final void setValues(List<PostAdAttributeValue> values) {
        this.values = values;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setPriceSensitive(Boolean priceSensitive) {
        this.priceSensitive = priceSensitive;
    }

    public Optional<PostAdAttributeValue> getSelectedValue() {
        for(PostAdAttributeValue value: values) {
            if (value.isSelected()) {
                return Optional.of(value);
            }
        }

        return Optional.empty();
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder class for PostAdAttribute objects.
     */
    public static final class Builder {

        private PostAdAttribute attribute = new PostAdAttribute();

        /**
         * Set whether the attribute should be mandatory.
         * @param mandatory true if the attribute should be mandatory
         * @return the updated builder
         */
        public Builder mandatory(boolean mandatory) {
            attribute.setMandatory(mandatory);
            return this;
        }

        /**
         * Set the ID for the attribute
         * @param id the ID
         * @return the updated builder
         */
        public Builder id(String id) {
            attribute.setId(id);
            return this;
        }

        /**
         * Set the type of the attribute.
         * @param type the type
         * @return the updated builder
         */
        public Builder type(PostAdAttributeType type) {
            attribute.setType(type);
            return this;
        }

        /**
         * Set the label for the attribute
         * @param label the label
         * @return the updated builder
         */
        public Builder label(String label) {
            attribute.setLabel(label);
            return this;
        }

        /**
         * Set the attribute values and category ID
         * @param value internal value
         * @param displayValue display value
         * @return the updated builder
         */
        public Builder withValue(String value, String displayValue) {
            PostAdAttributeValue attributeValue = new PostAdAttributeValue();
            attributeValue.setValue(value);
            attributeValue.setDisplayValue(displayValue);
            attribute.getValues().add(attributeValue);
            return this;
        }

        public Builder withValues(List<PostAdAttributeValue> values) {
            attribute.getValues().addAll(values);
            return this;
        }

        public Builder displayLabel(Boolean displayLabel) {
            attribute.displayLabel = displayLabel;
            return this;
        }

        public Builder priceSensitive(Boolean priceSensitive) {
            attribute.priceSensitive = priceSensitive;
            return this;
        }

        public Builder description(String description) {
            if (StringUtils.hasText(description)) {
                attribute.description = description;
            }
            return this;
        }

        /**
         * Build the attribute
         * @return the attribute
         */
        public PostAdAttribute build() {
            return attribute;
        }
    }
}
