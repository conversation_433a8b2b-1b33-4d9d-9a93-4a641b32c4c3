package com.gumtree.web.seller.page.reviews.service;

import com.gumtree.api.HystrixUtils;
import com.gumtree.userreviewsservice.client.ReviewsReadApi;
import com.gumtree.userreviewsservice.client.model.ApiRatingBreakdownItem;
import com.gumtree.userreviewsservice.client.model.GetSummaryRatingReceivedResponse;
import com.gumtree.util.ExceptionUtils;
import com.gumtree.web.seller.page.reviews.model.UserRating;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.Single;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class UserReviewsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserReviewsService.class);

    private final ReviewsReadApi reviewsReadApi;

    public UserReviewsService(ReviewsReadApi reviewsReadApi) {
        this.reviewsReadApi = reviewsReadApi;
    }

    public Single<Optional<UserRating>> getUserRating(Long accountID) {
        if (accountID == null) {
            return Single.just(Optional.empty());
        }

        return reviewsReadApi.findRatingReceivedByRevieweeId(accountID)
                .map(UserReviewsService::convertToUserRating)
                .map(Optional::of)
                .onErrorReturn(UserReviewsService::handleGetUserRatingError);
    }


    public static Optional<UserRating> handleGetUserRatingError(Throwable error) {
        Throwable unwrappedErr = HystrixUtils.unwrapThrowable(error);
        LOGGER.error("Unexpected error (handled) when retrieving an user rating. Message:  " + ExceptionUtils.toShortMessage(unwrappedErr));
        return Optional.empty();
    }

    private static UserRating convertToUserRating(GetSummaryRatingReceivedResponse response) {
        return new UserRating(
                getTotal(response),
                response.getAverageRating(),
                getBreakdown(response));
    }

    private static Integer getTotal(GetSummaryRatingReceivedResponse response) {
        return response.getTotalReceived() == null ? 0 : response.getTotalReceived();
    }

    private static Map<Integer, Integer> getBreakdown(GetSummaryRatingReceivedResponse response) {
        List<ApiRatingBreakdownItem> breakdown = response.getBreakdown() == null ? Collections.emptyList() : response.getBreakdown();
        return breakdown.stream()
                .collect(Collectors.toMap(ApiRatingBreakdownItem::getRating, ApiRatingBreakdownItem::getNumber));
    }

    public static String formatAvgRating(Float avgRating) {
        return avgRating == null ? "0" : String.format(java.util.Locale.UK, "%.1f", avgRating);
    }
}
