package com.gumtree.web.seller.page.logout.controller;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.exception.UnauthorizedUserException;
import com.gumtree.web.seller.page.CoreModelBaseController;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.logout.model.LogoutModel;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.gumtree.web.seller.security.apiauthentication.CSRFTokenService;
import com.gumtree.web.seller.security.apiauthentication.TimeSensitiveResponse;
import com.gumtree.web.seller.service.user.logout.UserLogoutService;
import com.gumtree.zeno.core.domain.PageType;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.net.URLDecoder;
import java.util.Collections;

import static com.gumtree.metrics.AuthenticationMetrics.Action;
import static com.gumtree.metrics.AuthenticationMetrics.Status;
import static com.gumtree.metrics.AuthenticationMetrics.incrementCounter;
import static com.gumtree.web.seller.security.apiauthentication.CsrfTokenMetricsUtils.recordInvalidCsrfTokenMetric;
import static com.gumtree.web.seller.security.apiauthentication.CsrfTokenMetricsUtils.recordValidCsrfTokenMetric;

@Controller
@GumtreePage(PageType.Logout)
public final class LogoutController extends CoreModelBaseController {

    public static final String PAGE_PATH = "/logout";

    private final MeterRegistry meterRegistry;
    private final UserLogoutService userLogoutService;
    private final CSRFTokenService csrfTokenService;

    @Autowired
    public LogoutController(CookieResolver cookieResolver,
                            CategoryModel categoryModel,
                            UserSessionService userSessionService,
                            UserLogoutService userLogoutService,
                            MeterRegistry meterRegistry,
                            CSRFTokenService csrfTokenService) {
        super(userSessionService, categoryModel, cookieResolver);

        this.userLogoutService = userLogoutService;
        this.meterRegistry = meterRegistry;
        this.csrfTokenService = csrfTokenService;
    }

    @RequestMapping(value = PAGE_PATH, method = RequestMethod.GET)
    public ModelAndView logout(HttpServletRequest request, HttpServletResponse response) {
        userLogoutService.logoutUser(request, response);

        incrementCounter(meterRegistry, Action.LOGOUT, Status.SUCCESS);

        CoreModel.Builder coreModelBuilder = getCoreModelBuilder(request).
                withGaEvents(Lists.newArrayList("LogoutEvent")).
                withUser(Optional.absent());
        return LogoutModel.builder().build(coreModelBuilder);
    }

    @RequestMapping(value = PAGE_PATH, method = RequestMethod.POST)
    public ModelAndView logout(@RequestParam(value = "token") String token,
                               HttpServletRequest request,
                               HttpServletResponse response) throws Exception {
        CoreModel.Builder coreModelBuilder = getCoreModelBuilder(request);
        if (getLoggedInUser().isPresent()) {
            String email = getLoggedInUser().get().getEmail();

            verifyCsrfToken(token, email);

            userLogoutService.logoutUser(request, response);
            incrementCounter(meterRegistry, Action.LOGOUT, Status.SUCCESS);
            coreModelBuilder.withGaEvents(Lists.newArrayList("LogoutEvent"))
                    .withUser(Optional.absent());

        } else {
            incrementCounter(meterRegistry, Action.LOGOUT, Status.FAILURE);
        }
        return LogoutModel.builder().build(coreModelBuilder);

    }

    private void verifyCsrfToken(String token, String email) throws Exception {
        String decodedToken = URLDecoder.decode(token, ParameterEncryption.ISO_8859_1);
        TimeSensitiveResponse<Object> timeSensitiveResponse
                = csrfTokenService.verifyCsrfTokenForEmail(decodedToken, email);

        if (!timeSensitiveResponse.isValid()) {
            recordInvalidCsrfTokenMetric(meterRegistry);
            throw new UnauthorizedUserException(Collections.singletonMap("token", token));
        }
        recordValidCsrfTokenMetric(meterRegistry);
    }
}
