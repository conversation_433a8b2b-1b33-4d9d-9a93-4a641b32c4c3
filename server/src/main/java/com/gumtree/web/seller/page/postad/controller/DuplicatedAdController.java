package com.gumtree.web.seller.page.postad.controller;


import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.postad.model.DuplicatedAdModel;
import com.gumtree.zeno.core.domain.PageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

@Controller
@GumtreePage(PageType.OrderPostAdError)
@GoogleAnalytics
public class DuplicatedAdController extends BaseSellerController {

    public static final String PAGE_PATH = "/postad/duplicated";

    @Autowired
    public DuplicatedAdController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                  ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                  UrlScheme urlScheme, UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
    }

    @RequestMapping(value = PAGE_PATH, method = RequestMethod.GET)
    public ModelAndView logout(HttpServletRequest request) {
        return DuplicatedAdModel.builder().build(getCoreModelBuilder(request));

    }

}
