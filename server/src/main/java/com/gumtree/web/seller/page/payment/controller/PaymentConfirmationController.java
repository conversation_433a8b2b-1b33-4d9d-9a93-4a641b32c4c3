package com.gumtree.web.seller.page.payment.controller;

import com.codahale.metrics.Meter;
import com.codahale.metrics.MetricRegistry;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.seller.domain.order.status.OrderStatus;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.payment.model.PayPalReturnResponse;
import com.gumtree.web.seller.page.payment.services.PaymentSuccessService;
import com.gumtree.web.seller.service.CheckoutContainer;
import com.gumtree.web.zeno.checkout.CheckoutInput;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.checkout.CheckoutSuccessEvent;
import com.gumtree.zeno.core.service.ZenoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletResponse;

import static com.codahale.metrics.MetricRegistry.name;
import static com.gumtree.web.seller.page.payment.controller.PaymentConfirmationController.PAGE_PATH;

/**
 * Responsive version of PaymentConfirmationController. Paypal bug means return URLs have query params
 * removed so we provide an definitive path for returning to responsive flow.
 */
@Controller
@GumtreePage(PageType.OrderSuccess)
@RequestMapping(PAGE_PATH)
public class PaymentConfirmationController extends BaseSellerController {

  private static final Logger LOGGER = LoggerFactory.getLogger(PaymentConfirmationController.class);

  public static final String PAGE_PATH = "/payment/complete";

  private BushfireApi bushfireApi;

  private CheckoutContainer checkoutContainer;
  private final ZenoService zenoService;
  private Meter ipnCallbackMeter;
  private final PaymentSuccessService paymentSuccessService;

  @Autowired
  public PaymentConfirmationController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                       ApiCallExecutor apiCallExecutor,
                                       ErrorMessageResolver messageResolver,
                                       UrlScheme urlScheme, BushfireApi bushfireApi,
                                       CheckoutContainer checkoutContainer,
                                       ZenoService zenoService,
                                       MetricRegistry metricRegistry,
                                       UserSessionService userSessionService,
                                       PaymentSuccessService paymentSuccessService) {
    super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
    this.bushfireApi = bushfireApi;
    this.checkoutContainer = checkoutContainer;
    this.zenoService = zenoService;
    ipnCallbackMeter = metricRegistry.meter(
        name(PaymentConfirmationController.class, "ipn", "callbacks"));
    this.paymentSuccessService = paymentSuccessService;
  }

  /**
   * Entry point for returns from PayPal card payments
   *
   * @param tx                 - paypal transaction ID
   * @param redirectAttributes - model to populate
   * @return view
   */
  @RequestMapping(method = RequestMethod.GET)
  public final String validatePayPalReturn(@RequestParam String tx, RedirectAttributes redirectAttributes) {
    return validatePayment(tx, redirectAttributes, false);
  }

  /**
   * Entry point for returns from PayPal account payments
   *
   * @param returnResponse     - paypal request containing transaction id
   * @param redirectAttributes - model to populate
   * @param response           - http servlet response
   * @return view
   */
  @RequestMapping(method = RequestMethod.POST)
  public final String validatePayPalAccountReturn(
      @ModelAttribute("returnResponse") PayPalReturnResponse returnResponse,
      RedirectAttributes redirectAttributes,
      HttpServletResponse response) {

    String redirectUrl = validatePayment(returnResponse.getTransactionId(), redirectAttributes, true);

    if (returnResponse.getInvoice() != null) {
      // IPN call, so don't redirect
      response.setStatus(HttpStatus.OK.value());
      ipnCallbackMeter.mark();

      return null;
    }

    return redirectUrl;
  }

  private String validatePayment(String transactionRef, RedirectAttributes redirectAttributes, boolean ipnCall) {
    try {
      ApiOrder order = bushfireApi.paymentApi().validatePayPalPayment(transactionRef);

      if (order != null && order.getStatus().equals(OrderStatus.PAID)) {
        bushfireApi.orderApi().executeTransaction(order.getId());
        Checkout checkout = checkoutContainer.createCheckout(order);

        LOGGER.info("Valid payment, redirecting to responsive success page: transaction=" + transactionRef);

        String response = createRedirect(paymentSuccessService.getPaymentSuccessUrl(checkout));

        zenoService.logEvent(new CheckoutInput(order, checkout, ipnCall ? "IPN" : "THANK_PAGE"),
            response, CheckoutSuccessEvent.class);

        return response;
      }
    } catch (RuntimeException ex) {
      LOGGER.error("Validate Payment Failed for transaction " + transactionRef, ex);
      // TODO: There should probably be somewhere special for this type of logging...
    }
    redirectAttributes.addFlashAttribute("transactionRef", transactionRef);

    String response = createRedirect(PaymentFailureController.PAGE_PATH);

    zenoService.logEvent(new CheckoutInput(null, null, ipnCall ? "IPN" : "THANK_PAGE"),
        response, CheckoutSuccessEvent.class);

    return response;
  }
}
