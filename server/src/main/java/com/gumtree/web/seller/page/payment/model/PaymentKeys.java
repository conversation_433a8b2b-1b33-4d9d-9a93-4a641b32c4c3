package com.gumtree.web.seller.page.payment.model;

import java.util.Objects;

public class PaymentKeys {

    private final String merchantId;
    private final Object token;
    private final String env;

    public PaymentKeys(String merchantId, Object token, String env) {
        this.merchantId = merchantId;
        this.token = token;
        this.env = env;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public Object getToken() {
        return token;
    }

    public String getEnv() {
        return env;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PaymentKeys that = (PaymentKeys) o;
        return Objects.equals(merchantId, that.merchantId) &&
                Objects.equals(token, that.token) &&
                Objects.equals(env, that.env);
    }

    @Override
    public int hashCode() {
        return Objects.hash(merchantId, token, env);
    }
}
