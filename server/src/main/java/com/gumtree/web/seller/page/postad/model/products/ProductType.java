package com.gumtree.web.seller.page.postad.model.products;

import com.google.common.base.Function;
import com.google.common.base.Predicate;
import com.google.common.collect.Iterables;
import com.gumtree.api.ApiProductPrice;
import com.gumtree.seller.domain.product.entity.ProductName;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Represents the different types of product that can be used within the post/edit ad flow.
 */
public enum ProductType {

    INSERTION("Insert an ad"),
    BUMP_UP("Bump up your ad"),
    URGENT("Up to 3 times more views and replies*. Perfect if you need to sell, rent or hire quickly."),
    FEATURED("Up to 7 times more views and replies*."
            + " Your ad will appear at the top of the listings for 3, 7 or 14 days."),
    SPOTLIGHT("Your ad will appear on the Gumtree homepage and will be seen by millions of people."),
    WEBSITE_URL("Website link"),
    NOT_VISIBLE_PRODUCT("Products that are not visible on edit screen.");

    private String description;

    /**
     * Constructor.
     *
     * @param description the description for this feature type.
     */
    ProductType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Get {@link ProductType} for a given {@link ProductName}.
     *
     * @param productName the product name.
     * @return {@link ProductType} for a given {@link ProductName}.
     */
    public static ProductType getType(ProductName productName) {

        switch (productName) {

            case INSERTION:
                return INSERTION;

            case BUMP_UP:
                return BUMP_UP;

            case FEATURE_3_DAY:
            case FEATURE_7_DAY:
            case FEATURE_14_DAY:
                return FEATURED;

            case URGENT:
                return URGENT;

            case HOMEPAGE_SPOTLIGHT:
                return SPOTLIGHT;

            case WEBSITE_URL:
                return WEBSITE_URL;

            case SEARCH_STANDOUT:
            case EXTENDED_VEHICLE_HISTORY_CHECK:
            case CALL_TRACKING_ACCOUNT_LEVEL:
            case CALL_TRACKING_ACCOUNT_LEVEL_HIDDEN:
            case CALL_TRACKING_ADVERT_LEVEL:
            case CALL_TRACKING_ADVERT_LEVEL_WITH_BLACKLISTING:
            case EMG_FREESPEE_PERMISSION:
                return NOT_VISIBLE_PRODUCT;

            default:
                throw new IllegalArgumentException("Unsupported product name: " + productName.name());
        }
    }

    /**
     * Extract map of products keyed by type from a list of {@link ApiProductPrice}s.
     *
     * @param prices the list of {@link ApiProductPrice}s.
     * @return map of product prices
     */
    public static Map<ProductType, List<ProductPrice>> extract(List<ApiProductPrice> prices) {
        Map<ProductType, List<ProductPrice>> pricesMap = new LinkedHashMap<>();
        Iterable<ProductPrice> products = Iterables.transform(prices, new ProductPriceConverter());

        for (ProductType featureType : values()) {
            List<ProductPrice> filteredPrices = new ArrayList<>();
            Iterables.addAll(filteredPrices, Iterables.filter(products, new ProductTypeFilter(featureType)));
            pricesMap.put(featureType, filteredPrices);
        }
        return pricesMap;
    }

    /**
     * Extract map of product prices keyed by product name
     *
     * @param prices the list of {@link ApiProductPrice}s
     * @return map of prices
     */
    public static Map<String, ProductPrice> productPriceMap(List<ApiProductPrice> prices) {
        Map<String, ProductPrice> priceMap = new HashMap<>();

        ProductPriceConverter converter = new ProductPriceConverter();
        for (ApiProductPrice productPrice : prices) {
            priceMap.put(productPrice.getProductName().name(), converter.apply(productPrice));
        }
        return priceMap;
    }

    /**
     * Function for transforming an {@link ApiProductPrice} into a {@link ProductPrice}.
     */
    private static final class ProductPriceConverter implements Function<ApiProductPrice, ProductPrice> {

        @Override
        public ProductPrice apply(@Nullable ApiProductPrice input) {

            ProductName product = input.getProductName();

            switch (product) {

                case INSERTION:
                    return new DefaultProductPrice(INSERTION, product.name(), input.getIncVat());

                case BUMP_UP:
                    return new DefaultProductPrice(BUMP_UP, product.name(), input.getIncVat());

                case FEATURE_3_DAY:
                    return new DefaultProductPrice(FEATURED, product.name(), 3, input.getIncVat());

                case FEATURE_7_DAY:
                    return new DefaultProductPrice(FEATURED, product.name(), 7, input.getIncVat());

                case FEATURE_14_DAY:
                    return new DefaultProductPrice(FEATURED, product.name(), 14, input.getIncVat());

                case URGENT:
                    return new DefaultProductPrice(URGENT, product.name(), 7, input.getIncVat());

                case HOMEPAGE_SPOTLIGHT:
                    return new DefaultProductPrice(SPOTLIGHT, product.name(), 7, input.getIncVat());

                case WEBSITE_URL:
                    return new DefaultProductPrice(WEBSITE_URL, product.name(), input.getIncVat());

                default:
                    return null;
            }
        }
    }

    /**
     * For filtering {@link ProductPrice}s by type.
     */
    private static final class ProductTypeFilter implements Predicate<ProductPrice> {

        private ProductType type;

        /**
         * Constructor.
         *
         * @param type to filter type
         */
        private ProductTypeFilter(ProductType type) {
            this.type = type;
        }

        @Override
        public boolean apply(@Nullable ProductPrice input) {
            return input != null && input.getProductType().equals(type);
        }
    }
}
