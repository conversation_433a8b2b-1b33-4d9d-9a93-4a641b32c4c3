package com.gumtree.web.seller.page.postad.model;

public class PostAdImage {

    private Long id;
    private String url;
    private String size;
    private String thumbnailUrl;

    public final String getUrl() {
        return url;
    }

    public final String getSize() {
        return size;
    }

    public final Long getId() {
        return id;
    }

    public final void setSize(String size) {
        this.size = size;
    }

    public final void setUrl(String url) {
        this.url = url;
    }

    public final void setId(Long id) {
        this.id = id;
    }

    public final String getThumbnailUrl() {
        return thumbnailUrl;
    }

    public final void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PostAdImage that = (PostAdImage) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (url != null ? !url.equals(that.url) : that.url != null) return false;
        if (size != null ? !size.equals(that.size) : that.size != null) return false;
        return thumbnailUrl != null ? thumbnailUrl.equals(that.thumbnailUrl) : that.thumbnailUrl == null;

    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (url != null ? url.hashCode() : 0);
        result = 31 * result + (size != null ? size.hashCode() : 0);
        result = 31 * result + (thumbnailUrl != null ? thumbnailUrl.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "PostAdImage{" +
                "id=" + id +
                ", url='" + url + '\'' +
                ", size='" + size + '\'' +
                ", thumbnailUrl='" + thumbnailUrl + '\'' +
                '}';
    }

    /**
     * Builder class for PostAdImage objects.
     */
    public static class Builder {
        private Long id;
        private String size;
        private String url;
        private String thumbnailUrl;

        /**
         * Set an id for the image
         *
         * @param id - the id to use
         * @return this
         */
        public final Builder id(Long id) {
            this.id = id;
            return this;
        }

        /**
         * Set a size for the image
         *
         * @param size - the size to use
         * @return this
         */
        public final Builder size(String size) {
            this.size = size;
            return this;
        }

        /**
         * Set a url for the image
         *
         * @param url - the url to use
         * @return this
         */
        public final Builder url(String url) {
            if(url != null && url.startsWith("http://")) {
                this.url = url.replaceFirst("http://","//");
            } else {
                this.url = url;
            }
            return this;
        }

        /**
         * Set a thumbnail url for the image
         *
         * @param thumbnailUrl - the thumbnail url to use
         * @return this
         */
        public final Builder thumbnailUrl(String thumbnailUrl) {
            this.thumbnailUrl = thumbnailUrl;
            return this;
        }

        /**
         * Build the image object.
         * @return the image..
         */
        public final PostAdImage build() {
            PostAdImage image = new PostAdImage();
            image.setId(id);
            image.setSize(size);
            image.setThumbnailUrl(thumbnailUrl);
            image.setUrl(url);
            return image;
        }
    }

}
