package com.gumtree.web.seller.page.postad.model;

import com.gumtree.api.AdStatus;
import com.gumtree.api.Image;
import com.gumtree.common.util.error.ErrorReporter;
import com.gumtree.common.util.error.ReportableErrors;
import com.gumtree.common.util.error.ReportableErrorsArguments;
import com.gumtree.common.util.error.SimpleError;
import com.gumtree.util.UUIDValidator;
import com.gumtree.web.seller.page.common.model.GaElement;
import com.gumtree.web.seller.page.postad.model.location.PostcodeLookupResponse;
import com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.page.postad.validation.AdTitleHasPhoneNumberValidation;
import com.gumtree.web.seller.page.postad.validation.AdTitleXssValidation;
import com.gumtree.web.seller.page.postad.validation.DescriptionHasPhoneNumberValidation;
import com.gumtree.web.seller.page.postad.validation.DescriptionLengthValidation;
import com.gumtree.web.seller.page.postad.validation.MinImagesPostedCategory;
import com.gumtree.web.seller.page.postad.validation.SpotlightFeatureValidation;

import org.codehaus.jackson.annotate.JsonAutoDetect;
import org.joda.time.DateTime;
import org.springframework.util.StringUtils;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState.POSTCODE_NOT_FOUND;
import static com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState.POSTCODE_UNDEFINED;

@SpotlightFeatureValidation(fieldList = {"features['SPOTLIGHT'].selected" }, message = "postad.spotlight.needs_image")
@AdTitleHasPhoneNumberValidation(fieldList = "title", message = "postad.title.phonenumber.invalid")
@AdTitleXssValidation(fieldList = "title", message = "postad.title.xss.opentag")
@DescriptionHasPhoneNumberValidation(fieldList = "description", message = "postad.description.containPhoneNumber")
@DescriptionLengthValidation(fieldList = "description", message = "postad.description.tooShort")
@MinImagesPostedCategory(fieldList = "file", message = "postad.pets.needs_image")
@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,
        getterVisibility = JsonAutoDetect.Visibility.NONE,
        setterVisibility = JsonAutoDetect.Visibility.NONE
)
public final class PostAdDetail implements Serializable, ReportableErrors {

    private Long advertId;

    private Long categoryId;

    private Long locationId;

    private String postcode;

    private PostcodeSelectionState state = POSTCODE_UNDEFINED;

    @Valid
    private PostAdFormBean postAdFormBean;

    private List<Image> images = new ArrayList<>();

    private AdStatus status;

    // This stores details of any existing features for ads being edited.
    private Map<ProductType, DateTime> existingFeatures = new HashMap<>();

    private boolean recentlyPublished = false;

    private DateTime publishedDate = null;

    private String ipAddress;

    private String threatmetrixSessionId;

    private String cookie;

    /**
     * What this draft loaded from the draft ads storage ? (If Yes, we need to show popup to user informing him
     * that he has draft which I can finish if he wants)
     */
    private Boolean draft;

    private boolean vrnWidgetFlow;

    private List<GaElement> gaEvents;

    public Long getAdvertId() {
        return advertId;
    }

    public void setAdvertId(Long advertId) {
        this.advertId = advertId;
    }

    public Long getCategoryId() {
        return postAdFormBean != null ? postAdFormBean.getCategoryId() : null;
    }

    public Long getLocationId() {
        return locationId;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setStatus(AdStatus status) {
        this.status = status;
    }

    public Boolean isAdvertExpired() {
        return status == AdStatus.EXPIRED;
    }

    public Boolean isAdvertDraft() {
        return status == AdStatus.DRAFT;
    }

    public Boolean isAdvertAwaitingPhoneVerified() {
        return status != null && status == AdStatus.AWAITING_PHONE_VERIFIED;
    }

    public PostAdFormBean getPostAdFormBean() {
        return postAdFormBean;
    }

    public boolean isVrnWidgetFlow() {
        return vrnWidgetFlow;
    }

    public void setVrnWidgetFlow(boolean vrnWidgetFlow) {
        this.vrnWidgetFlow = vrnWidgetFlow;
    }

    public List<GaElement> getGaEvents() {
        return gaEvents;
    }

    public void setGaEvents(List<GaElement> gaEvents) {
        this.gaEvents = gaEvents;
    }

    public void addGaEvent(GaElement event) {
        if (event != null) {
            if (this.gaEvents == null) {
                this.gaEvents = new ArrayList<>();
            }

            this.gaEvents.add(event);
        }
    }

    public String getThreatmetrixSessionId() {
        return threatmetrixSessionId;
    }

    public void setThreatmetrixSessionId(String threatmetrixSessionId) {
        if(threatmetrixSessionId != null && UUIDValidator.isValid(threatmetrixSessionId)) {
            this.threatmetrixSessionId = threatmetrixSessionId;
        }
    }

    /**
     * Only to be set via AdvertEditor - see AdvertEditorImpl.setPostAdFormBean(PostAdFormBean postAdFormBean)
     */
    public void setPostAdFormBean(PostAdFormBean postAdFormBean) {
        this.postAdFormBean = postAdFormBean;
    }

    /**
     * @param categoryId
     */
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
        this.postAdFormBean.setCategoryId(categoryId);
    }

    public void setPostcodeLocation(PostcodeLookupResponse response) {
        this.state = response.getState();
        if(response.isOutcodeRecognised()) {
            this.postcode = null;
            this.postAdFormBean.setPostcode(null);
            this.state = POSTCODE_UNDEFINED;
        } else {
            this.postcode = response.getPostcode();
            this.postAdFormBean.setPostcode(response.getPostcode());
        }
        this.locationId = response.getLocationId();
        this.postAdFormBean.setLocationId(response.getLocationId());
    }

    public void setManualLocation(Long locationId) {
        this.state = POSTCODE_UNDEFINED;
        this.postcode = null;
        this.locationId = locationId;
        this.postAdFormBean.setPostcode(null);
        this.postAdFormBean.setLocationId(locationId);
    }

    public PostcodeSelectionState getPostcodeState() {
        return state;
    }

    public void setPostCodeNotFound() {
        this.state = POSTCODE_NOT_FOUND;
    }

    /**
     * Get list of images or initialise if not already existing.
     *
     * @return images.
     */
    public List<Image> getImages() {
        if (images == null) return new ArrayList<>();
        return images;
    }

    public void setImages(List<Image> images) {
        this.images = images;
    }

    public void addExistingFeature(ProductType productType, DateTime expiryDate) {
        this.existingFeatures.put(productType, expiryDate);
    }

    public Map<ProductType, DateTime> getExistingFeatures() {
        return existingFeatures;
    }

    public void setExistingFeatures(Map<ProductType, DateTime> existingFeatures) {
        this.existingFeatures = existingFeatures;
    }

    public boolean isRecentlyPublished() {
        return recentlyPublished;
    }

    public void setRecentlyPublished(boolean recentlyPublished) {
        this.recentlyPublished = recentlyPublished;
    }

    public DateTime getPublishedDate() {
        return publishedDate;
    }

    public void setPublishedDate(DateTime publishedDate) {
        this.publishedDate = publishedDate;
    }

    public String getCookie() {
        return cookie;
    }

    public void setCookie(String cookie) {
        this.cookie = cookie;
    }

    public final String getIpAddress() {
        return ipAddress;
    }

    public final void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public Boolean isDraft() {
        return draft != null && draft;
    }

    public void setDraft(Boolean draft) {
        this.draft = draft;
    }

    @Override
    public void report(ErrorReporter errorReporter) {
        for (SimpleError error : state.generateErrorsForState()) {
            report(errorReporter, error, error.getArgs());
        }
    }

    @Override
    public void report(ErrorReporter errorReporter, ReportableErrorsArguments args) {
        for (SimpleError error : state.generateErrorsForState()) {
            report(errorReporter, error, args.getCombinedArguments(error));
        }
    }

    private void report(ErrorReporter errorReporter, SimpleError error, Object[] combinedArguments) {
        if (StringUtils.hasText(error.getField())) {
            errorReporter.fieldError(
                    error.getField(),
                    error.getMessageCode(),
                    error.getDefaultMessage(),
                    combinedArguments);
        } else {
            errorReporter.globalError(
                    error.getMessageCode(),
                    error.getDefaultMessage(),
                    combinedArguments);
        }
    }

    public AdStatus getStatus() {
        return this.status;
    }
}
