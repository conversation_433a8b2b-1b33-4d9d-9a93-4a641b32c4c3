package com.gumtree.web.seller.page.postad.converter;

import com.gumtree.api.Image;
import com.gumtree.web.seller.page.postad.model.PostAdImage;

/**
 * Interface for a converter that can convert an Image into a PostAdImage.
 */
public interface PostAdImageConverter {

    /**
     * Convert the image.
     * @param image image to convert
     * @return the post ad image
     */
    PostAdImage convertImageToPostAdImage(Image image);
}
