package com.gumtree.web.seller.page.context;

import com.gumtree.api.Account;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.web.common.page.context.GumtreeCommonPageContext;
import com.gumtree.web.common.page.handler.PageHandler;
import com.gumtree.web.security.UserSession;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Enhanced page context object for use in seller public.
 */
public class GumtreeSellerPageContext extends GumtreeCommonPageContext {

    @Autowired
    private UserSession authenticatedUserSession;

    @Autowired
    private BushfireApi bushfireApi;

    private Long accountId;
    private Account account;

    @Override
    public void postInit(HttpServletRequest req, HttpServletResponse resp, PageHandler method) {
        if (authenticatedUserSession.isAuthenticated()) {
            setUser(authenticatedUserSession.getUser());
            accountId = authenticatedUserSession.getSelectedAccountId();
        }
    }

    @Override
    public void setAccount(Account account) {
        this.account = account;
    }

    @Override
    public final Account getAccount() {
        if (accountId != null && account == null) {
            account = bushfireApi.accountApi().getAccount(accountId);
        }
        return account;
    }
}
