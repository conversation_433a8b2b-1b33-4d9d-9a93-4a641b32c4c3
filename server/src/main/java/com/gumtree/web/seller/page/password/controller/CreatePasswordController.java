package com.gumtree.web.seller.page.password.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.domain.user.beans.CreatePasswordBean;
import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.password.api.CreatePasswordApiCall;
import com.gumtree.web.seller.page.password.api.ValidateActivationKeyApiCall;
import com.gumtree.web.seller.page.password.model.UpdatePasswordForm;
import com.gumtree.web.seller.page.password.model.UpdatePasswordModel;
import com.gumtree.zeno.core.domain.PageType;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * Create password controller to handle user activation for users that have not created a password yet (e.g.
 * salesforce)
 */
@Controller
@GoogleAnalytics
@GumtreePage(PageType.PasswordReset)
public class CreatePasswordController extends BaseSellerController {

    public static final String PAGE_PATH = "/create-password";
    private static final String USER_ID_PARAM = "id";
    private static final String ACTIVATION_KEY_PARAM = "key";

    @Autowired
    public CreatePasswordController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                    ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                    UrlScheme urlScheme, UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
    }

    @RequestMapping(method = RequestMethod.GET, value = PAGE_PATH)
    public final ModelAndView showPage(@RequestParam(value = USER_ID_PARAM, required = false) String username,
                                       @RequestParam(value = ACTIVATION_KEY_PARAM, required = false) String activationKey,
                                       HttpServletRequest request)
            throws IOException {
        try {
            Assert.hasLength(username);
            Assert.hasLength(activationKey);

            if (validActivationKey(username, activationKey).isErrorResponse()) {
                return errorResponse(request);
            }
            return getModelAndView(username, activationKey, request);
        } catch (Exception e) {
            return errorResponse(request);
        }
    }

    /**
     * Method to create a new password and activate an account.
     *
     * @param username
     * @param activationKey
     * @param password
     * @param confirmedPassword
     * @param model
     * @param subject
     * @param request
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = PAGE_PATH)
    public final ModelAndView createPassword(@RequestParam(value = USER_ID_PARAM, required = false) String username,
                                             @RequestParam(value = ACTIVATION_KEY_PARAM, required = false)
                                                     String activationKey,
                                             @RequestParam(value = "form.password", required = false)
                                                     String password,
                                             @RequestParam(value = "form.confirmedPassword", required = false)
                                                     String confirmedPassword,
                                             Model model, Subject subject, HttpServletRequest request) {
        if (validActivationKey(username, activationKey).isErrorResponse()) {
            return errorResponse(request);
        }

        CreatePasswordBean createPasswordBean = new CreatePasswordBean();
        createPasswordBean.setActivationKey(activationKey);
        createPasswordBean.setPassword(password);
        createPasswordBean.setConfirmedPassword(confirmedPassword);

        ApiCallResponse response = execute(new CreatePasswordApiCall(username, createPasswordBean));
        if (response.isErrorResponse()) {
            populateErrors(model, response);
            return getModelAndView(username, activationKey, request);
        }

        UsernamePasswordToken token = new UsernamePasswordToken(username, password);
        token.setRememberMe(true);
        subject.login(token);

        return redirect(getUrlScheme().urlFor(Actions.BUSHFIRE_MANAGE_ADS));
    }

    private ModelAndView getModelAndView(String username, String activationKey, HttpServletRequest request) {
        final String formAction = buildFormAction(
                getUrlScheme().urlFor(Actions.BUSHFIRE_CREATE_PASSWORD), username, activationKey);

        CoreModel.Builder coreModel = getCoreModelBuilder(request);
        UpdatePasswordModel.Builder updatePasswordModel = newUpdatePasswordModel()
                .withId(username)
                .withKey(activationKey)
                .withFormAction(formAction);

        return updatePasswordModel.build(coreModel);
    }

    private ModelAndView errorResponse(HttpServletRequest request) {
        CoreModel.Builder coreModel = getCoreModelBuilder(request);
        UpdatePasswordModel.Builder updatePasswordModel = newUpdatePasswordModel()
                .withValidationErrors()
                .withLoginLink(getUrlScheme().urlFor(Actions.BUSHFIRE_LOGIN))
                .withForgottenPasswordLink(getUrlScheme().urlFor(Actions.BUSHFIRE_FORGOTTEN_PASSWORD));

        return updatePasswordModel.build(coreModel);
    }

    private UpdatePasswordModel.Builder newUpdatePasswordModel() {
        return UpdatePasswordModel.builder()
                .withPage(Page.CreatePassword)
                .withActionName("Create")
                .withUpdatePasswordBean(new UpdatePasswordForm());
    }

    private String buildFormAction(String path, String userId, String activationKey) {
        return new StringBuilder()
                .append(path).append("?")
                .append(USER_ID_PARAM).append("=").append(userId).append("&")
                .append(ACTIVATION_KEY_PARAM).append("=").append(activationKey)
                .toString();
    }

    private ApiCallResponse validActivationKey(String username, String activationKey) {
        return execute(new ValidateActivationKeyApiCall(username, activationKey));
    }
}
