package com.gumtree.web.seller.page.postad.controller;

import com.google.gson.Gson;
import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.security.phone.number.authenticator.model.AuthenticationStatus;
import com.gumtree.seller.domain.order.status.OrderStatus;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.ip.DefaultRemoteIP;
import com.gumtree.web.common.ip.RemoteIP;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.manageads.api.CreateOrderApiCall;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.payment.controller.PaymentCheckoutController;
import com.gumtree.web.seller.page.payment.model.CheckoutScene;
import com.gumtree.web.seller.page.payment.services.PaymentSuccessService;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.SingleAdvertShoppingCart;
import com.gumtree.web.seller.page.postad.model.path.ManageAdsPath;
import com.gumtree.web.seller.page.postad.model.path.PostAdStatePath;
import com.gumtree.web.seller.service.CheckoutContainer;
import com.gumtree.web.seller.service.CheckoutMetaInjector;
import com.gumtree.web.seller.service.PostAdWorkspace;
import com.gumtree.web.seller.service.phoneverify.PhoneVerifyService;
import com.gumtree.zeno.core.domain.PageType;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.QueryParam;
import java.io.PrintWriter;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.gumtree.web.security.shiro.NewUserLoginFilter.PHONE_VERIFY_PLATFORM;

@Controller
@RequestMapping(PostAdStatePath.MAPPING)
@GumtreePage(PageType.PostAd)
public final class PostAdSyncPhoneVerifyStateController extends BasePostAdController implements InitializingBean {

  private static final Logger logger = LoggerFactory.getLogger(PostAdSyncPhoneVerifyStateController.class);

  private CustomMetricRegistry metrics;

  private BushfireApi bushfireApi;

  private UserSession userSession;

  private CookieResolver cookieResolver;

  private CheckoutContainer checkoutContainer;

  private CheckoutMetaInjector checkoutMetaInjector;

  @Autowired
  private PhoneVerifyService phoneVerifyService;
  private PaymentSuccessService paymentSuccessService;

  private final ExecutorService executorService;

  @Autowired
  public PostAdSyncPhoneVerifyStateController(
      CookieResolver cookieResolver,
      CategoryModel categoryModel,
      ApiCallExecutor apiCallExecutor,
      ErrorMessageResolver messageResolver,
      UrlScheme urlScheme,
      PostAdWorkspace postAdWorkspace,
      UserSession userSession,
      CategoryService categoryService,
      GumtreePageContext pageContext,
      CheckoutContainer checkoutContainer,
      LocationService locationService,
      CheckoutMetaInjector checkoutMetaInjector,
      UserSessionService userSessionService, CustomMetricRegistry metrics, BushfireApi bushfireApi,
      PaymentSuccessService paymentSuccessService) {
    super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, postAdWorkspace, userSession,
        categoryService, pageContext, locationService, userSessionService);
    this.metrics = metrics;
    this.bushfireApi = bushfireApi;
    this.cookieResolver = cookieResolver;
    this.checkoutContainer = checkoutContainer;
    this.checkoutMetaInjector = checkoutMetaInjector;
    this.paymentSuccessService = paymentSuccessService;

    this.executorService = new ThreadPoolExecutor(4, 4, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100));
  }

  @Override
  public void afterPropertiesSet() throws Exception {
  }

  @RequestMapping(method = RequestMethod.GET, value = "/{userId}/verifyFinishedForApp")
  public void syncPhoneVerifyState(
      @PathVariable Long userId
      , @QueryParam("adId") Long adId
      , @QueryParam("editorId") String editorId
      , HttpServletRequest request
      , HttpServletResponse response
  ) {
    response.setContentType("application/json");
    response.setCharacterEncoding("UTF-8");
    PrintWriter responsePrintWriter = null;

    try {
      responsePrintWriter = response.getWriter();

      ModelAndView modelAndView = handSync(userId, adId, editorId, request);
      ModelMap map = modelAndView.getModelMap();
      String resp = new Gson().toJson(map);
      responsePrintWriter.println(resp);
      response.setStatus(HttpStatus.OK.value());

    } catch (Exception e) {
      if (responsePrintWriter != null) {
        responsePrintWriter.println("{\"bizCode\":\"SYSTEM_ERROR\",\"msg\":\"system_error\"}");
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
      }
    } finally {
      if (responsePrintWriter != null) {
        responsePrintWriter.close();
      }
    }
  }

  @RequestMapping(method = RequestMethod.GET, value = "/{adId}/{editorId}/verifyFinished")
  public ModelAndView syncPhoneVerifyState(
      @PathVariable Long adId
      , @PathVariable String editorId
      , HttpServletRequest request
  ) {
    Long userId = getAuthenticatedUserSession().getUser().getId();
    return handSync(userId, adId, editorId, request);
  }

  ModelAndView handSync(Long userId, Long adId, String editorId, HttpServletRequest request) {
    try {
      // adId query adInfo
      Ad ad = bushfireApi.advertApi().getAdvert(adId);

      // judge adStatus
      if (ad.getStatus() != AdStatus.AWAITING_PHONE_VERIFIED) {
        return createResultView("STATUS_NOT_MATCH", "The ad status is not match");
      }

      //double check phone verification
      AuthenticationStatus statusSingle = phoneVerifyService.getAuthenticationStatus(userId);
      if (statusSingle == null || !statusSingle.equals(AuthenticationStatus.VERIFIED)) {
        return createResultView("USER_UNVERIFIED", "The user not complete phone verification");
      }

      AdvertEditor editor = getAdvertEditorAndUpdateAdStatus(editorId, request, ad);

      ApiCallResponse<Ad> postAdResponse = execute(editor);
      if (postAdResponse.isErrorResponse()) {
        return createResultView("SYSTEM_ERROR", "system error");
      }

      ApiCallResponse<ApiOrder> createOrderResponse = metrics.syncPhoneVerifyStateTimer("createOrder").record(() ->
          createOrder(postAdResponse.getResponseObject(), editor, request));

      metrics.syncPhoneVerifyStateTimer("removeEditor").record(() ->
          getPostAdWorkspace().removeAllEditor(editor.getEditorId(), editor.isCreateMode()));

      if (createOrderResponse == null || createOrderResponse.isErrorResponse()) {
        return createResultView("SYSTEM_ERROR", "system error");
      }

      Checkout checkout = metrics.syncPhoneVerifyStateTimer("createCheckout").record(() ->
          checkoutContainer.createCheckout(createOrderResponse.getResponseObject(), postAdResponse.getResponseObject(), true, CheckoutScene.PHONE_VERIFY));

      long start = System.currentTimeMillis();

      Checkout finalCheckout = (checkoutMetaInjector.injectTrackingForPost(checkout, postAdResponse.getResponseObject()));
      metrics.syncPhoneVerifyStateTimer("injectTracking").record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);

      //judge invoker , web will return commonly , ios & android will return checkout
      if (isWebRequest(request)) {
        return getRedirectForCheckout(PaymentCheckoutController.PAGE_PATH, finalCheckout);
      }
      //if order is paid ( free ), not need return checkout
      if (createOrderResponse.getResponseObject().getStatus() == OrderStatus.PAID) {

        String thankYouTracking = paymentSuccessService.getPaymentSuccessUrl(finalCheckout).split("\\?")[1];

        return createResultView("SUCCESS", "success", "thankUrl", thankYouTracking + "&advertId=" + adId);
      }

      return createResultView("SUCCESS", "success",
          "payUrl", PaymentCheckoutController.checkoutFormAction(finalCheckout.getKey()));
    } catch (Exception e) {
      logger.error("sync error ", e);
      return createResultView("SYSTEM_ERROR", "system error");
    }
  }

  private AdvertEditor getAdvertEditorAndUpdateAdStatus(String editorId, HttpServletRequest request, Ad ad) {
    AdvertEditor editor = null;
    if (StringUtils.isNotBlank(editorId)) {
      try {
        editor = getPostAdWorkspace().getEditorByEditorId(editorId);
      } catch (Exception e) {
        logger.warn("getEditor by editorId error", e);
      }
    }

    if (editor == null) {
      PermanentCookie permanentCookie = cookieResolver.resolve(request, PermanentCookie.class);
      ThreatMetrixCookie threatMetrixCookie = cookieResolver.resolve(request, ThreatMetrixCookie.class);

      RemoteIP remoteIP = getRemoteIP(request);
      editor = getPostAdWorkspace().createAndPersistEditor(ad.getId(), ad.getCategoryId(), remoteIP, permanentCookie, threatMetrixCookie);
    }

    editor.getAdvertDetail().getPostAdFormBean().addExtendField("scene", "verifyFinished");

    return editor;
  }

  private RemoteIP getRemoteIP(HttpServletRequest request) {
    String ipAddress = request.getRemoteAddr();

    if (StringUtils.isBlank(ipAddress)) {
      ipAddress = request.getHeader("X-Forwarded-For");
    }
    if (StringUtils.isBlank(ipAddress)) {
      ipAddress = request.getHeader("X-Real-IP");
    }
    if (StringUtils.isBlank(ipAddress)) {
      ipAddress = "0.0.0.0";
    }


    return new DefaultRemoteIP(ipAddress);
  }

  private ApiCallResponse<ApiOrder> createOrder(Ad ad, AdvertEditor editor, HttpServletRequest request) {
    SingleAdvertShoppingCart cart = new SingleAdvertShoppingCart(ad.getId(), getAccountId(request));
    editor.populateShoppingCart(cart);

    CreateOrderApiCall apiCall = new CreateOrderApiCall(cart.toOrderBean(), getAuthenticatedUserSession());
    return execute(apiCall);
  }

  private boolean isWebRequest(HttpServletRequest request) {
    String platform = request.getHeader(PHONE_VERIFY_PLATFORM);
    return !StringUtils.isNotBlank(platform) || !platform.equals("app");
  }

  private ModelAndView createResultView(String code, String message, Object... keyValues) {
    ModelMap modelMap = new ModelMap()
        .addAttribute("bizCode", code)
        .addAttribute("msg", message);

    if (keyValues != null && keyValues.length > 0) {
      if (keyValues.length % 2 != 0) {
        throw new IllegalArgumentException("Key-value pairs must be provided in pairs");
      }

      for (int i = 0; i < keyValues.length; i += 2) {
        modelMap.addAttribute(keyValues[i].toString(), keyValues[i + 1]);
      }
    }
    return redirect(new ManageAdsPath().getPath()).addAllObjects(modelMap);
  }

  private Long getAccountId(HttpServletRequest request) {
    Long accountId = getAccountId();
    if (accountId == null) {
      accountId = Long.parseLong(request.getHeader("userId"));
    }
    return accountId;
  }
}
