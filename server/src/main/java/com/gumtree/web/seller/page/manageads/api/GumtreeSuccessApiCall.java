package com.gumtree.web.seller.page.manageads.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.domain.advert.DeleteReason;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;

public class GumtreeSuccessApiCall extends AuthenticatedApiCall<Void> {

    private DeleteReason deleteReason;

    private String advertId;

    /**
     * Constructor
     *
     * @param advertId       - advert id
     * @param deleteReason   - delete reason bean
     * @param apiKeyProvider - user's api key
     */
    public GumtreeSuccessApiCall(String advertId, DeleteReason deleteReason, ApiKeyProvider apiKeyProvider) {
        super(apiKeyProvider);
        this.advertId = advertId;
        this.deleteReason = deleteReason;
    }

    @Override
    public Void execute(BushfireApi api) {
        api.create(AdvertApi.class, getApiKey()).newDeleteReason(advertId, deleteReason);
        return null;
    }
}
