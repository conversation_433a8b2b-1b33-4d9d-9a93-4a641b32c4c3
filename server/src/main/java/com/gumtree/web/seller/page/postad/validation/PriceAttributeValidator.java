package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.api.category.domain.CategoryConstants;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;
import java.util.Map;

/**
 * Validator for price attribute.
 */
public final class PriceAttributeValidator implements ConstraintValidator<ValidPrice, Map<String, String>> {

    @Override
    public void initialize(ValidPrice constraintAnnotation) {

    }

    @Override
    public boolean isValid(Map<String, String> attributes, ConstraintValidatorContext context) {

        String priceValue = attributes.get(CategoryConstants.Attribute.PRICE.getName());

        if (StringUtils.hasLength(priceValue)) {

            priceValue = priceValue.replaceAll(",", "").trim();

            try {

                new BigDecimal(priceValue).multiply(new BigDecimal(100));

                return true;

            } catch (NumberFormatException ex) {

                return false;
            }
        }

        return true;
    }
}
