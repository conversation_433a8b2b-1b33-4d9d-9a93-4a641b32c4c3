package com.gumtree.web.seller.page.postad.model.path;

import com.gumtree.web.common.path.AbstractPath;

import static com.gumtree.web.common.path.PathBuilder.path;

public class PostAdStatePath extends AbstractPath {

    private String editorId;

    private static final String PREFIX = "/postad";
    public static final String MAPPING_EDITOR = "/{editorId}";

    public static final String MAPPING = PREFIX;

    public PostAdStatePath(String editorId) {
        this.editorId = editorId;
    }

    @Override
    public String getPathSegment() {
        return path()
                .addPathSegment(PREFIX)
                .addPathSegment(editorId)
                .build();
    }
}
