package com.gumtree.web.seller.page.postad.api;

import com.gumtree.api.Images;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.ImageApi;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;
import org.springframework.web.multipart.MultipartFile;

/**
 * API call wrapper for posting an image
 */
public class PostImageApiCall extends AuthenticatedApiCall<Images> {

    private MultipartFile image;

    /**
     * Constructor.
     * @param apiKeyProvider the API key provider
     * @param image the multipart image file
     */
    public PostImageApiCall(ApiKeyProvider apiKeyProvider, MultipartFile image) {
        super(apiKeyProvider);
        this.image = image;
    }

    @Override
    public final Images execute(BushfireApi api) {
        return api.create(ImageApi.class, getApiKey()).doUpload(image);
    }
}
