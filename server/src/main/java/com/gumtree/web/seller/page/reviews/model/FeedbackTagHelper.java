package com.gumtree.web.seller.page.reviews.model;

import com.gumtree.userreviewsservice.client.model.NegativeTagName;
import com.gumtree.userreviewsservice.client.model.PositiveTagName;

public final class FeedbackTagHelper {

    private FeedbackTagHelper() {}

    public static boolean isPositive(String tag) {
        return null != PositiveTagName.fromValue(tag);
    }

    public static boolean isNegative(String tag) {
        return null != NegativeTagName.fromValue(tag);
    }

    public static boolean isValid(String tag) {
        return isPositive(tag) || isNegative(tag);
    }

}
