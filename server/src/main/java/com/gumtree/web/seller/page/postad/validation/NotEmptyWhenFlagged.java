package com.gumtree.web.seller.page.postad.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 *
 */
@Target({ TYPE, ANNOTATION_TYPE })
@Retention(RUNTIME)
@Constraint(validatedBy = NotEmptyWhenFlaggedValidator.class)
@Documented
public @interface NotEmptyWhenFlagged {

    /**
     * Get the validation message.
     */
    String message() default "{com.moa.podium.util.constraints.matches}";

    /**
     * Get the validation groups.
     */
    Class<?>[] groups() default { };

    /**
     * Get the validation payload.
     */
    Class<? extends Payload>[] payload() default { };

    /**
     * Form field to be validated
     */
    String field();

    /**
     * Flag field
     */
    String flagField();

    /**
     * Defines several <code>@NotEmptyWhenFlagged</code> annotations on the same element
     *
     * @see NotEmptyWhenFlagged
     */
    @Target({ TYPE, ANNOTATION_TYPE })
    @Retention(RUNTIME)
    @Documented
    @interface List {
        NotEmptyWhenFlagged[] value();
    }

}
