package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class DescriptionLengthValidator implements ConstraintValidator<DescriptionLengthValidation, PostAdDetail> {

    private final ErrorMessageResolver errorMessageResolver;
    private String message;

    @Autowired
    public DescriptionLengthValidator(ErrorMessageResolver errorMessageResolver) {
        this.errorMessageResolver = errorMessageResolver;
    }

    private String[] fieldList;

    @Override
    public void initialize(DescriptionLengthValidation constraintAnnotation) {
        this.fieldList = constraintAnnotation.fieldList();
        this.message = constraintAnnotation.message();
    }

    @Override
    public boolean isValid(PostAdDetail value, ConstraintValidatorContext context) {
        final String description = ObjectUtils.firstNonNull(value.getPostAdFormBean().getDescription(), "");

        if (!hasValidLength(description)) {
            final String errorMessage = errorMessageResolver.getMessage(message, "", ValidationUtils.DESCRIPTION_MIN_CHAR_LENGTH);
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(errorMessage)
                    .addPropertyNode(fieldList[0])
                    .addConstraintViolation();
            return false;
        }
        return true;
    }

    private boolean hasValidLength(String description) {
        return description.trim().length() >= ValidationUtils.DESCRIPTION_MIN_CHAR_LENGTH;
    }
}
