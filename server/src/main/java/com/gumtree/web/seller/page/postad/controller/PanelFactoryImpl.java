package com.gumtree.web.seller.page.postad.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.service.pricing.PricingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PanelFactoryImpl implements PanelFactory {

    @Autowired
    private CategoryModel categoryModel;

    @Autowired
    private BushfireApi bushfireApi;

    @Autowired
    private PricingService pricingService;

    @Override
    public SellerTypePanel createSellerTypePanel(AdvertEditor editor, AttributeMetadata attributeMetadata) {
        boolean inMotors = categoryModel.isChild(CategoryConstants.MOTORS_ID, editor.getCategoryId());
        boolean inProperties = categoryModel.isChild(CategoryConstants.FLATS_AND_HOUSES_ID, editor.getCategoryId());

        if (inMotors) {
            return new MandatorySellerTypePanel(bushfireApi, editor, attributeMetadata, pricingService, categoryModel);
        } else if (inProperties) {
            return new MandatorySellerTypePanel(bushfireApi, editor, attributeMetadata, pricingService, categoryModel);
        } else {
            return new SellerTypePanelForSales(bushfireApi, editor, attributeMetadata);
        }
    }
}
