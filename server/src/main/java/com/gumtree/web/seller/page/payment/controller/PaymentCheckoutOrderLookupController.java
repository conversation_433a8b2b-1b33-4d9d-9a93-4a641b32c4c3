package com.gumtree.web.seller.page.payment.controller;

import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.spec.OrderApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.service.CheckoutContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 */
@Controller
public final class PaymentCheckoutOrderLookupController extends BaseSellerController {

    private BushfireApi bushfireApi;

    private CheckoutContainer checkoutContainer;

    private UserSession authenticatedUserSession;

    @Autowired
    public PaymentCheckoutOrderLookupController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                                ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                                UrlScheme urlScheme, BushfireApi bushfireApi,
                                                CheckoutContainer checkoutContainer,
                                                UserSession authenticatedUserSession,
                                                UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.bushfireApi = bushfireApi;
        this.checkoutContainer = checkoutContainer;
        this.authenticatedUserSession = authenticatedUserSession;
    }

    /**
     * Find order in checkout session, and redirect to that checkout page
     *
     *
     * @param orderId - order id to lookup
     * @return redirect to checkout page
     */
    @RequestMapping(value = "/checkout/order/{orderId}")
    public ModelAndView findOrderKeyAndRedirect(@PathVariable("orderId") String orderId, Model model) {
        BushfireApiKey apiKey = authenticatedUserSession.getApiKey();
        ApiOrder order = bushfireApi.create(OrderApi.class, apiKey).getOrder(Long.valueOf(orderId));

        Checkout checkout = checkoutContainer.createCheckout(order);
        return getRedirectForCheckout(PaymentCheckoutController.PAGE_PATH, checkout);
    }
}
