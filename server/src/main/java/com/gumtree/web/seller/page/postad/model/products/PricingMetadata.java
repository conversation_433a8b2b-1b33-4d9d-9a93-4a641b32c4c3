package com.gumtree.web.seller.page.postad.model.products;

import java.util.Map;

/**
 * Metadata for product prices within post/edit ad flow.
 */
public interface PricingMetadata {

    /**
     * @return the price for insertion
     */
    ProductPrice getInsertionPrice();

    /**
     * @return the price for bump up
     */
    ProductPrice getBumpUpPrice();

    /**
     * @return the list of feature options
     */
    Map<ProductType, FeatureOption> getFeatureOptions();

    /**
     * Get the feature option by product type
     *
     * @param  type product type
     * @return feature option by product type
     */
    FeatureOption getFeatureOption(ProductType type);

    /**
     * Get the feature option by product type string - for freemarker
     *
     * @param type
     * @return
     */
    FeatureOption getFeatureOption(String type);

    /**
     * Get price for a feature
     *
     * @param type        the product type
     * @param productName the product name
     * @return price for product
     */
    ProductPrice getFeaturePrice(ProductType type, String productName);
}
