package com.gumtree.web.seller.page.payment.controller;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.domain.payment.ApiPayPalIPNBean;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.filter.paypal.PayPalIPNRequest;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.payment.converter.PayPalIPNRequestToApiPayPalIPNBeanConverter;
import org.jboss.resteasy.client.ClientResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.ws.rs.core.Response;

import static com.codahale.metrics.MetricRegistry.name;

/**
 * Controller for handling IPN callbacks from PayPal.
 * <p/>
 * IMPORTANT: This controller relies on {@link com.gumtree.web.filter.paypal.PayPalIPNFilter} to grab the raw request body.
 * This is necessary because if Spring processes the request body it alters the parameter order, and we need the
 * original parameter order so we can verify the IPN callback against the PayPal servers.
 */
@Controller
public class PayPalIPNCallbackController extends BaseSellerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PayPalIPNCallbackController.class);

    public static final String PAGE_PATH = "/payment/ipn";

    private final BushfireApi bushfireApi;

    private final PayPalIPNRequestToApiPayPalIPNBeanConverter converter;

    private final PayPalIPNRequest payPalIPNRequest;

    private final Counter ipnCallbackCounter;
    private final Counter ipnCallbackSuccessCounter;
    private final Counter ipnCallbackFailureCounter;

    @Autowired
    public PayPalIPNCallbackController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                       ApiCallExecutor apiCallExecutor,
                                       ErrorMessageResolver messageResolver,
                                       UrlScheme urlScheme, BushfireApi bushfireApi,
                                       MetricRegistry metricRegistry,
                                       PayPalIPNRequest payPalIPNRequest,
                                       PayPalIPNRequestToApiPayPalIPNBeanConverter converter,
                                       UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.bushfireApi = bushfireApi;
        this.payPalIPNRequest = payPalIPNRequest;
        this.converter = converter;
        ipnCallbackCounter = metricRegistry.counter(
                name(PayPalIPNCallbackController.class, "ipn", "callbacks"));
        ipnCallbackSuccessCounter = metricRegistry.counter(
                name(PayPalIPNCallbackController.class, "ipn", "callbacks", "success"));
        ipnCallbackFailureCounter = metricRegistry.counter(
                name(PayPalIPNCallbackController.class, "ipn", "callbacks", "failure"));
    }

    /**
     * Entry point for PayPal IPN callbacks.
     */
    @RequestMapping(value = PayPalIPNCallbackController.PAGE_PATH, method = RequestMethod.POST)
    @ResponseBody
    public final ResponseEntity<String> processIPNCallback() {

        ipnCallbackCounter.inc();

        try {
            ApiPayPalIPNBean apiPayPalIPNBean = converter.convert(payPalIPNRequest);
            ClientResponse<Void> response = bushfireApi.paymentApi().processIPNCallback(apiPayPalIPNBean);

            if (Response.Status.OK.equals(response.getResponseStatus())) {
                ipnCallbackSuccessCounter.inc();
                return new ResponseEntity<>("", HttpStatus.OK); // PayPal requires an empty string in the response body
            } else {
                ipnCallbackFailureCounter.inc();
                return new ResponseEntity<>(HttpStatus.valueOf(response.getResponseStatus().getStatusCode()));
            }
        } catch (Exception ex) {
            // We have no idea if the callback was processed, so return an error and log the problem.
            ipnCallbackFailureCounter.inc();
            LOGGER.error(String.format("PayPal IPN callback with body %s and content type %s was not processed",
                    payPalIPNRequest.getBody(), payPalIPNRequest.getContentType()), ex);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }


    }

}
