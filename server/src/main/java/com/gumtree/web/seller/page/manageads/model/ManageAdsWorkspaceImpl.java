package com.gumtree.web.seller.page.manageads.model;

import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.storage.SellerSessionDataService;

/**
 *  ManageAdsWorkspace Implementation
 */
public final class ManageAdsWorkspaceImpl implements ManageAdsWorkspace {

    private SellerSessionDataService sessionDataService;
    private UserSession userSession;


    /**
     * Constructor
     * @param sessionDataService - persistence service
     * @param userSession - detail about logged in user
     */
    public ManageAdsWorkspaceImpl(SellerSessionDataService sessionDataService, UserSession userSession) {
        this.sessionDataService = sessionDataService;
        this.userSession = userSession;
    }

    @Override
    public ManageAdsFilterFormBean getFilterForm() {
        return sessionDataService.getManageAdsFilterForm(getFilterFormKeyForUser());
    }

    @Override
    public void setFilterForm(ManageAdsFilterFormBean manageAdsFilterFormBean) {
        sessionDataService.setManageAdsFilterForm(getFilterFormKeyForUser(), manageAdsFilterFormBean);
    }

    @Override
    public void removeFilterForm() {
        sessionDataService.removeManageAdsFilterForm(getFilterFormKeyForUser());
    }

    private String getFilterFormKeyForUser() {
        return userSession.getUsername() + "-mad";
    }
}
