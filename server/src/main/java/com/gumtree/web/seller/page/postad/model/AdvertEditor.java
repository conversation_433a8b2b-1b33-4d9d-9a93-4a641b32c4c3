package com.gumtree.web.seller.page.postad.model;

import com.google.common.base.Optional;
import com.gumtree.api.Ad;
import com.gumtree.api.Image;
import com.gumtree.api.User;
import com.gumtree.api.client.executor.ApiCall;
import com.gumtree.api.domain.advert.PostAdvertBean;
import com.gumtree.api.domain.user.beans.RegisterUserBean;
import com.gumtree.common.util.error.ReportableResponse;
import com.gumtree.web.seller.page.postad.service.legal.LegalBean;

import java.util.List;

/**
 * Represents the creating or editing of an advert.
 */
public interface AdvertEditor extends ApiCall<Ad> {

    /**
     * @return advert id, or null if in create mode
     */
    Long getAdvertId();

    /**
     * @return determines if this editor reflects a valid ad for create or edit.
     */
    Boolean isValid();

    /**
     * @return validation result
     */
    ReportableResponse validate();

    /**
     * @return the unique ID for the editor.
     */
    String getEditorId();

    /**
     * @return the current category
     */
    Long getCategoryId();

    Long getLocationId();

    Long getAccountId();

    User getUser();

    /**
     * @return the form containing current state of advert
     */
    PostAdFormBean getPostAdFormBean();

    /**
     * Update the advert details.
     *
     * @param postAdFormBean the form containing current state of advert
     */
    void setPostAdFormBean(PostAdFormBean postAdFormBean);

    /**
     * @return images associated with the current advert session
     */
    List<Image> getImages();

    /**
     * @return convert state into advert that can be posted to BAPI.
     */
    PostAdvertBean toApiBean();

    /**
     * @return convert data pertaining to user registration into API bean.
     */
    RegisterUserBean toRegisterUserBean();

    /**
     * Get the postcode bean
     * @return PostAdPostcodeBean
     */
    String getPostcode();

    /**
     * Populate the session bean with API ad data.
     *
     * @param advertId the API advert ID
     */
    void loadAdvert(Long advertId);

    void initNew(Long categoryId);

    /**
     * @return if the editor supports changing category
     */
    boolean supportsChangeCategory();

    /**
     * @return if the editor supports changing location
     */
    boolean supportsChangeLocation();

    boolean supportsPostToAnyLocation();

    Boolean supportsContactUrl();
    /**
     * @return if the advert is a new one or a current one being edited
     */
    boolean isCreateMode();

    boolean isEditMode();

    /**
     * @return if the editor supports changing visible on map
     */
    boolean supportsChangeVisibleOnMap();

    /**
     * @return a word describing what is being done to the ad - Post or Edit
     */
    String getDisplayActionVerb();

    /**
     * Populates shopping cart with products selected for this editor.
     *
     * @param shoppingCart the cart to populate.
     */
    void populateShoppingCart(ShoppingCart shoppingCart);

    /**
     * @return determines if the ad being edited would need to be bumped up for changes to be propogated to live.
     */
    boolean requiresBumpUp();

    /**
     * @return determines if ad is not in create mode and not in draft mode
     */
    boolean supportsBumpUp();

    /**
     *
     * @return determines if ad is in draft mode
     */
    boolean isDraftMode();

    /**
     *
     * @return the detail this editor wraps
     */
    PostAdDetail getAdvertDetail();

    Optional<LegalBean> getLegalRequirements();

    Boolean isValidCategorySelected();

    Boolean isValidLocationSelected();

    Boolean showInsertionPrice();

    Boolean isUserLoggedIn();

    void refreshPreferredContactDetailsOfNonProUser();

    void addImage(Image image, int position);

    void removeImage(Long imageId);

    void setCheckoutVariant(String checkoutVariationId);

    boolean isRelistForFree();
}
