package com.gumtree.web.seller.page.postad.model.products;

import java.util.Optional;

public enum AnimalType {

    DOG(9389L, "dog_breed"),
    CAT(9390L, "cat_breed");

    private final Long categoryId;
    private final String breedAttributeParentName;

    AnimalType(Long categoryId, String breedAttributeParentName) {
        this.categoryId = categoryId;
        this.breedAttributeParentName = breedAttributeParentName;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public String getBreedAttributeParentName() {
        return breedAttributeParentName;
    }

    public static Optional<AnimalType> fromCategoryId(Long categoryId) {
        for (AnimalType animalType : AnimalType.values()) {
            if (animalType.getCategoryId().equals(categoryId)) {
                return Optional.of(animalType);
            }
        }
        return Optional.empty();
    }
}
