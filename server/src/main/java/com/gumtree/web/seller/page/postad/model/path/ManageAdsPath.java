package com.gumtree.web.seller.page.postad.model.path;

import com.gumtree.web.common.path.AbstractPath;

import static com.gumtree.web.common.path.PathBuilder.path;

public class ManageAdsPath extends AbstractPath {

    private static final String PREFIX = "manage";
    private static final String SUFFIX = "ads";

    public static final String MAPPING = "/" + PREFIX + "/" + SUFFIX;

    public ManageAdsPath() {
        // Empty on purpose
    }

    @Override
    public String getPathSegment() {
        return path()
                .addPathSegment(PREFIX)
                .addPathSegment(SUFFIX)
                .build();
    }
}
