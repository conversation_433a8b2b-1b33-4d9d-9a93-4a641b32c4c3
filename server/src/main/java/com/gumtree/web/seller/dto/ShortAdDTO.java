package com.gumtree.web.seller.dto;

import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.domain.advert.AdvertStatus;

public class ShortAdDTO {

    private Long id;
    private String title;
    private AdStatus status;

    public ShortAdDTO(Ad ad) {
        this.id = ad.getId();
        this.title = ad.getTitle();
        this.status = ad.getStatus();
    }

    public Long getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public AdStatus getStatus() {
        return status;
    }
}
