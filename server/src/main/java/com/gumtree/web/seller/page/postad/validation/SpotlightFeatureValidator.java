package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.features.FeatureBean;
import com.gumtree.web.seller.page.postad.model.products.ProductType;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Map;

/**
 *
 */
public class SpotlightFeatureValidator implements ConstraintValidator<SpotlightFeatureValidation, PostAdDetail> {

    private String message;

    private String[] fieldList;

    @Override
    public final void initialize(SpotlightFeatureValidation constraintAnnotation) {
        this.message = constraintAnnotation.message();
        this.fieldList = constraintAnnotation.fieldList();
    }

    @Override
    public final boolean isValid(PostAdDetail value, ConstraintValidatorContext context) {
        boolean result = true;
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addNode(fieldList[0]).addConstraintViolation();

        PostAdFormBean formBean = value.getPostAdFormBean();
        if(formBean != null) {
            Map<ProductType, FeatureBean> featuresMap = formBean.getFeatures();
            FeatureBean bean = featuresMap != null ? featuresMap.get(ProductType.SPOTLIGHT) : null;

            if (bean != null && bean.getSelected() != null && bean.getSelected()) {
                result = value.getImages() != null && value.getImages().size() > 0;
            }
        }
        return result;
    }
}
