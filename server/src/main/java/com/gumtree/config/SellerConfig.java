package com.gumtree.config;

import com.gumtree.analytics.config.GoogleAnalyticsConfig;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.properties.utils.PropSupplier;
import com.gumtree.config.api.AccountServiceApiConfig;
import com.gumtree.config.api.AdCountersConfig;
import com.gumtree.config.api.BapiContractConfig;
import com.gumtree.config.api.BushfireApiProfileConfig;
import com.gumtree.config.api.CategoryPredictApiConfig;
import com.gumtree.config.api.CategoryReadApiConfig;
import com.gumtree.config.api.DraftsApiConfig;
import com.gumtree.config.api.FeignClientConfiguration;
import com.gumtree.config.api.LocationApiConfig;
import com.gumtree.config.api.MediaProcessorApiConfig;
import com.gumtree.config.api.MotorsApiConfig;
import com.gumtree.config.api.MotorsPriceGuidanceServiceConfig;
import com.gumtree.config.api.PaymentApiConfig;
import com.gumtree.config.api.PhoneNumberAuthenticatorApiConfig;
import com.gumtree.config.api.UserAdPreferenceServiceApiConfig;
import com.gumtree.config.api.UserResolverConfig;
import com.gumtree.config.api.UserReviewsApiConfig;
import com.gumtree.config.api.UserServiceApiConfig;
import com.gumtree.config.cookies.CookieConfig;
import com.gumtree.config.device.DeviceConfig;
import com.gumtree.config.models.ModelsConfig;
import com.gumtree.config.service.BapiLocationServiceConfig;
import com.gumtree.config.session.SellerSessionStorageConfig;
import com.gumtree.config.thirdparty.SellerThirdPartyConfig;
import com.gumtree.service.jobs.CvApiConfig;
import com.gumtree.util.UuidProvider;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.util.url.impl.UrlSchemeImpl;
import com.gumtree.web.abtest.ExperimentsProvider;
import com.gumtree.web.browse.BrowseCategoriesLocationResolvingService;
import com.gumtree.web.browse.BrowseCategoriesService;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.feature.FeatureSwitchManager;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.security.apiauthentication.CSRFTokenService;
import com.gumtree.web.seller.service.securetoken.DefaultSecureTokenService;
import com.gumtree.web.seller.service.securetoken.SecureTokenService;
import com.gumtree.web.storage.strategy.SessionPersistenceStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.ResourceHttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.feed.AtomFeedHttpMessageConverter;
import org.springframework.http.converter.feed.RssChannelHttpMessageConverter;
import org.springframework.http.converter.json.MappingJacksonHttpMessageConverter;
import org.springframework.http.converter.xml.Jaxb2RootElementHttpMessageConverter;
import org.springframework.http.converter.xml.SourceHttpMessageConverter;
import org.springframework.http.converter.xml.XmlAwareFormHttpMessageConverter;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.ClassUtils;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.util.List;
import java.util.UUID;

import static com.gumtree.web.seller.page.common.model.CoreModel.BuilderProps;

@EnableScheduling
@Configuration
@Import({
    SellerPropertiesConfig.class,
    SellerLoggingConfig.class,
    CategoryReadApiConfig.class,
    BushfireApiProfileConfig.class,
    DraftsApiConfig.class,
    CvApiConfig.class,
    ModelsConfig.class,
    SellerSessionStorageConfig.class,
    SellerDraftAdConfig.class,
    SellerCacheConfig.class,
    SellerSecurityConfig.class,
    ContentConfig.class,
    SellerThirdPartyConfig.class,
    SellerExceptionHandlingConfig.class,
    SellerMetricsConfig.class,
    PayPalIPNConfig.class,
    SellerCorsFilterConfig.class,
    CookieConfig.class,
    DeviceConfig.class,
    FeatureToggleConfig.class,
    SellerCvUploadConfig.class,
    GoogleAnalyticsConfig.class,
    MotorsApiConfig.class,
    PaymentApiConfig.class,
    UserReviewsApiConfig.class,
    SellerImageServiceConfig.class,
    UserServiceApiConfig.class,
    UserAdPreferenceServiceApiConfig.class,
    AccountServiceApiConfig.class,
    ConvertrConfig.class,
    UserResolverConfig.class,
    BapiLocationServiceConfig.class,
    BrowseCategoriesService.class,
    BrowseCategoriesLocationResolvingService.class,
    LocationApiConfig.class,
    BapiContractConfig.class,
    FeignClientConfiguration.class,
    MediaProcessorApiConfig.class,
    MotorsPriceGuidanceServiceConfig.class,
    AdCountersConfig.class,
    GoogleAuthConfig.class,
    SellerApiAuthenticationConfig.class,
    CategoryPredictApiConfig.class,
    PhoneNumberAuthenticatorApiConfig.class,
})
@ComponentScan(basePackages = {
        "com.gumtree.web.abtest",
        "com.gumtree.web.adsense",
        "com.gumtree.web.service.bapi",
        "com.gumtree.common.model.location",
        "com.gumtree.google",
        "com.gumtree.domain",
        "com.gumtree.web.seller.converter",
        "com.gumtree.common.format",
        "com.gumtree.service.location",
        "com.gumtree.util.url",
        "com.gumtree.web.advertising.partnership",
        "com.gumtree.legacy"
})
public class SellerConfig extends WebMvcConfigurerAdapter {

    /**
     * Nasty hack to override Spring's behaviour to favour Jackson 2.x over Jackson 1.9. For some reason,
     * our AJAX calls do not like Jackson 2.x. So this block of code copied from
     * {@link org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport}
     * and tweaked adds the default message converters but forces the user of Jackson 1.9.
     * TODO: Upgrade our applications to use Jackson 2.2 and remove this block of code
     *
     * @param messageConverters message converters added so far
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> messageConverters) {
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter();
        stringConverter.setWriteAcceptCharset(false);

        messageConverters.add(new ByteArrayHttpMessageConverter());
        messageConverters.add(stringConverter);
        messageConverters.add(new ResourceHttpMessageConverter());
        messageConverters.add(new SourceHttpMessageConverter<>());
        messageConverters.add(new XmlAwareFormHttpMessageConverter());

        ClassLoader classLoader = getClass().getClassLoader();
        if (ClassUtils.isPresent("javax.xml.bind.Binder", classLoader)) {
            messageConverters.add(new Jaxb2RootElementHttpMessageConverter());
        }
        if (ClassUtils.isPresent("org.codehaus.jackson.map.ObjectMapper", classLoader)) {
            messageConverters.add(new MappingJacksonHttpMessageConverter());
        }
        if (ClassUtils.isPresent("com.sun.syndication.feed.WireFeed", classLoader)) {
            messageConverters.add(new AtomFeedHttpMessageConverter());
            messageConverters.add(new RssChannelHttpMessageConverter());
        }
    }

    @Autowired
    private UserSession userSession;

    @Autowired
    @Qualifier("secureTokenPersistenceStrategy")
    private SessionPersistenceStrategy secureTokenPersistenceStrategy;

    @Autowired
    private BrowseCategoriesService browseCategoriesService;

    @Autowired
    private BrowseCategoriesLocationResolvingService browseCategoriesLocationResolvingService;

    @Autowired
    private CSRFTokenService csrfTokenService;

    @Bean
    public Resource siteProperties() {
        return new ClassPathResource("siteId.properties");
    }

    @Bean
    public Resource postingCategoryMappings() {
        return new ClassPathResource("postingCategoryMappings.csv");
    }

    @Bean
    public UrlScheme urlScheme() {
        return new UrlSchemeImpl();
    }

    @Bean
    public UuidProvider uuidProvider() {
        return UUID::randomUUID;
    }

    @Bean(name = "secureTokenService")
    public SecureTokenService secureTokenService() {
        return new DefaultSecureTokenService(secureTokenPersistenceStrategy, userSession);
    }

    @Bean
    public CoreModel.BuilderFactory coreModelBuilderFactory(CategoryModel categoryModel,
                                                            CookieResolver cookieResolver,
                                                            UserSessionService userSessionService,
                                                            ExperimentsProvider experimentsProvider,
                                                            FeatureSwitchManager featureSwitchManager) {
        String host = GtProps.getStr(SellerProperty.GUMTREE_HOST);
        String myGumtreeHost = GtProps.getStr(SellerProperty.MY_GUMTREE_HOST);
        boolean clientLogging = GtProps.getBool(SellerProperty.CLIENT_LOG_ENABLED);
        PropSupplier<Boolean> messageCentreEnabled = GtProps.getDBool(SellerProperty.MESSAGE_CENTRE_ENABLED);
        PropSupplier<Boolean> messageCentreLinkEnabled = GtProps.getDBool(SellerProperty.MESSAGE_CENTRE_LINK_ENABLED);
        PropSupplier<Integer> headerNotificationPollingFrequency = GtProps.getDInt(SellerProperty.NOTIFICATION_POLLING_FREQUENCY);

        BuilderProps props = new BuilderProps(
                GtProps.getEnv(),
                host,
                myGumtreeHost,
                clientLogging,
                messageCentreEnabled,
                messageCentreLinkEnabled,
                headerNotificationPollingFrequency);

        return new CoreModel.BuilderFactory(
                props,
                categoryModel,
                cookieResolver,
                userSessionService,
                experimentsProvider,
                browseCategoriesService,
                browseCategoriesLocationResolvingService,
                featureSwitchManager,
                csrfTokenService
        );
    }
}
