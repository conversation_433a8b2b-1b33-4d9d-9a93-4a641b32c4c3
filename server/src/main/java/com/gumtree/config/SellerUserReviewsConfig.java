package com.gumtree.config;

import com.gumtree.userreviewsservice.client.ReviewsReadApi;
import com.gumtree.web.seller.page.reviews.service.UserReviewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SellerUserReviewsConfig {
    @Autowired
    private ReviewsReadApi reviewsReadApi;

    @Bean
    public UserReviewsService userReviewsService() {
        return new UserReviewsService(reviewsReadApi);
    }
}
