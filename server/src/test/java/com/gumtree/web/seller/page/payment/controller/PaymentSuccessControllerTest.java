package com.gumtree.web.seller.page.payment.controller;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.Account;
import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.client.executor.ApiCall;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.error.ApiErrorCode;
import com.gumtree.mobile.test.Fixtures;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.service.category.impl.CategoryServiceImpl;
import com.gumtree.web.common.domain.order.converter.ApiOrderToOrderEntityConverter;
import com.gumtree.web.common.domain.order.entity.OrderEntity;
import com.gumtree.web.common.domain.order.entity.OrderItemEntity;
import com.gumtree.web.security.UserLoginStatus;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutAdvert;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.page.payment.model.PostAdErrorViewModel;
import com.gumtree.web.seller.page.payment.model.PostAdSuccessViewModel;
import com.gumtree.web.seller.page.payment.services.PaymentSuccessService;
import com.gumtree.web.seller.page.postad.model.PostAdFeatureBean;
import com.gumtree.web.seller.page.postad.model.features.FeatureBean;
import com.gumtree.web.seller.page.postad.model.features.FeatureProductBuilder;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.service.CheckoutContainer;
import com.gumtree.web.seller.service.CheckoutMetaInjector;
import com.gumtree.web.seller.service.convertr.ConvertrService;
import com.gumtree.web.seller.service.threatmetrix.ThreatMetrixService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Matchers;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.math.BigDecimal;
import java.util.Map;

import static com.google.common.collect.Lists.newArrayList;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyBoolean;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.withSettings;


@RunWith(MockitoJUnitRunner.class)
public class PaymentSuccessControllerTest extends BaseSellerControllerTest {

    private PaymentSuccessController controller;
    private UserSession userSession;
    private CheckoutContainer checkoutContainer;
    private CheckoutMetaInjector checkoutMetaInjector;
    private SuccessfulCheckoutManager successfulCheckoutManager;
    private ApiOrderToOrderEntityConverter orderEntityConverter;
    private FeatureProductBuilder featureProductBuilder;
    private ConvertrService convertrService;
    private PaymentSuccessService paymentSuccessService;
    private ThreatMetrixService threatMetrixService;

    private final static String USER_EMAIL = "<EMAIL>";
    private final static String AD_TITLE = "Test ad";
    private final static Long AD_ID = 1L;
    private static final String VIEW_AD_URL = "someUrl/" + AD_ID;


    @Before
    public void setUp() {

        Ad ad = new Ad();
        ad.setId(AD_ID);
        ad.setTitle(AD_TITLE);
        ad.setStatus(AdStatus.LIVE);
        ad.setImages(newArrayList());

        CheckoutAdvert cad = CheckoutAdvert.Builder.builder().id(1L).build();

        Category audi = Fixtures.STUB_CATEGORY_MODEL.getByName("audi").get();

        CheckoutImpl checkout = new CheckoutImpl();
        checkout.setAdvert(cad);

        ApiOrder apiOrder = new ApiOrder();
        apiOrder.setTotalIncVat(1L);
        apiOrder.setItems(newArrayList());

        OrderItemEntity orderItem = new OrderItemEntity();
        orderItem.setProductName(ProductName.INSERTION);

        OrderEntity order = new OrderEntity();
        order.setItems(newArrayList(orderItem));
        order.setTotalIncVat(new BigDecimal("1"));

        userSession = mock(UserSession.class, withSettings().defaultAnswer(RETURNS_DEEP_STUBS));
        checkoutContainer = mock(CheckoutContainer.class);
        orderEntityConverter = mock(ApiOrderToOrderEntityConverter.class);
        featureProductBuilder = mock(FeatureProductBuilder.class);
        checkoutMetaInjector = mock(CheckoutMetaInjector.class);
        successfulCheckoutManager = mock(SuccessfulCheckoutManager.class);
        convertrService = new ConvertrService(true, new CategoryServiceImpl(Fixtures.STUB_CATEGORY_MODEL));
        paymentSuccessService = mock(PaymentSuccessService.class);
        threatMetrixService = mock(ThreatMetrixService.class);

        when(checkoutMetaInjector.injectTrackingForEditorUpdate(any(Checkout.class), any(Ad.class), eq(AdStatus.LIVE))).thenReturn(createCheckout("xyz"));
        when(checkoutMetaInjector.injectTrackingForManageAdsUpdate(any(Checkout.class), anyBoolean(), any(Ad.class))).thenReturn(createCheckout("xyz"));
        when(categoryService.getById(anyLong())).thenReturn(Optional.of(audi));
        when(categoryService.getCategoriesList(any(Category.class))).thenReturn(Lists.newArrayList(Fixtures.createRootCategory().build(), Fixtures.MOTORS_CATEGORY, Fixtures.CARS_CATEGORY, audi));
        when(checkoutContainer.getCheckout(anyString())).thenReturn(checkout);
        when(successfulCheckoutManager.paymentConfirmed(any())).thenReturn(true);
        when(userSession.getUserType()).thenReturn(UserLoginStatus.EXISTING);
        when(userSession.getSelectedAccountId()).thenReturn(1L);
        when(userSession.getUsername()).thenReturn(USER_EMAIL);
        when(bushfireApi.advertApi().getAdvert(any())).thenReturn(ad);
        when(orderEntityConverter.convert(any())).thenReturn(order);
        when(featureProductBuilder.populateFeatureMap(any(), any())).thenReturn(null);
        when(successfulCheckoutManager.processPayment(any())).thenReturn(apiOrder);
        when(messageResolver.getMessage(any(), any())).thenReturn("");
        when(bushfireApi.accountApi().getAccount(anyLong())).thenReturn(new Account());
        when(urlScheme.urlFor(ad)).thenReturn(VIEW_AD_URL);

        AdvertApi adverApi = mock(AdvertApi.class);
        when(bushfireApi.advertApi()).thenReturn(adverApi);
        when(adverApi.getAdvert(eq(1L))).thenReturn(ad);
        controller = new PaymentSuccessController(cookieResolver, categoryModel, userSession, checkoutContainer, apiCallExecutor,
                messageResolver, urlScheme, successfulCheckoutManager, pageContext, categoryService, locationService
                , bushfireApi, featureProductBuilder, orderEntityConverter, zenoService, userSessionService, appBannerCookieHelper,
                checkoutMetaInjector, convertrService, paymentSuccessService,threatMetrixService);

        autowireAbExperimentsService(controller);
    }

    @Test
    public void testSuccessResponseFromBapiForFeatureApply() {
        ApiCallResponse<ApiOrder> response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(false);
        when(response.getResponseObject()).thenReturn(new ApiOrder());
        when(apiCallExecutor.call(Matchers.<ApiCall>any())).thenReturn(response);

        ModelAndView modelAndView = controller.applyFeature(createFeatureBean(), "1", request);
        assertThat(modelAndView.getView()).isInstanceOf(RedirectView.class);

        RedirectView redirectView = (RedirectView) modelAndView.getView();
        assertThat(redirectView.getUrl()).isEqualTo("/checkout/xyz");
    }

    @Test
    public void testErrorResponseFromBapiForFeatureApply() {
        ApiCallResponse<ApiOrder> response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(true);
        when(response.getErrorCode()).thenReturn(ApiErrorCode.ADVERT_ALREADY_REMOVED);
        when(apiCallExecutor.call(Matchers.<ApiCall>any())).thenReturn(response);

        ModelAndView modelAndView = controller.applyFeature(createFeatureBean(), "1", request);
        assertThat(modelAndView.getModel().get("model")).isInstanceOf(PostAdErrorViewModel.class);

        PostAdErrorViewModel model = (PostAdErrorViewModel) modelAndView.getModel().get("model");
        assertThat(model.getErrorCode()).isEqualTo("1E");
    }

    @Test
    public void populatePostAdSocialShareInfo() {
        // when
        ModelAndView x = controller.showBookingSuccessPage("x", request,response);
        PostAdSuccessViewModel model = (PostAdSuccessViewModel) x.getModel().get("model");

        // then
        assertEquals(String.valueOf(AD_ID), model.getUpsellAdvertId());
        assertEquals(AD_TITLE, model.getAdvertTitle());
        assertEquals(VIEW_AD_URL, model.getViewUrl());
    }

    // convert url tests
    @Test
    public void populateConvertrUrl() {
        // when
        ModelAndView x = controller.showBookingSuccessPage("x", request,response);
        PostAdSuccessViewModel model = (PostAdSuccessViewModel) x.getModel().get("model");

        // then
        assertThat(model.getConvertrUrl()).isEqualTo("https://gumtree.cvtr.io/forms/gumtree-v2?firstname&lastname&email&telephone_number&vehicle_reg&location=United+Kingdom");
    }

    // utility methods
    private PostAdFeatureBean createFeatureBean() {
        FeatureBean featured = new FeatureBean();
        featured.setSelected(true);
        featured.setProductName("FEATURE_3_DAY");

        FeatureBean spotlight = new FeatureBean();
        spotlight.setSelected(false);
        spotlight.setProductName("SPOTLIGHT");

        FeatureBean urgent = new FeatureBean();
        urgent.setSelected(false);
        urgent.setProductName("URGENT");

        PostAdFeatureBean postAdFeatureBean = new PostAdFeatureBean();
        Map<ProductType, FeatureBean> features = Maps.newHashMap();
        features.put(ProductType.FEATURED, featured);
        features.put(ProductType.SPOTLIGHT, spotlight);
        features.put(ProductType.URGENT, urgent);
        postAdFeatureBean.setFeatures(features);
        return postAdFeatureBean;
    }

    private Checkout createCheckout(String key) {
        Checkout checkout = new CheckoutImpl();
        checkout.setKey(key);
        return checkout;
    }
}
