package com.gumtree.web.seller.page.payment.service;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.category.domain.Category;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutAdvert;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.page.payment.services.PaymentSuccessService;
import com.gumtree.web.seller.page.postad.model.meta.MetaPathInfo;
import com.gumtree.web.seller.page.postad.model.meta.PageActionType;
import com.gumtree.web.seller.page.postad.model.meta.PagePaymentType;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PaymentSuccessServiceTest {
    private PaymentSuccessService paymentSuccessService;
    private CategoryService categoryService;
    private Category categoryCars;
    private Category categoryNonCars;
    private Category categorySubCars;

    @Before
    public void setup() {
        categoryService = mock(CategoryService.class);
        paymentSuccessService = new PaymentSuccessService(categoryService);
        categoryCars = new Category();
        categoryCars.setId(9311L);
        categoryCars.setSeoName("cars");
        categoryNonCars = new Category();
        categoryNonCars.setId(-9311L);

        categorySubCars = new Category();
        categorySubCars.setId(10432L);

        when(categoryService.getById(9311L)).thenReturn(Optional.of(categoryCars));
        when(categoryService.getById(-9311L)).thenReturn(Optional.of(categoryNonCars));

        when(categoryService.getCategoriesList(categoryCars)).thenReturn(Lists.newArrayList(categoryCars));
        when(categoryService.getCategoriesList(categoryNonCars)).thenReturn(Lists.newArrayList(categoryNonCars));


        when(categoryService.getById(10432L)).thenReturn(Optional.of(categorySubCars));
        when(categoryService.getCategoriesList(categorySubCars)).thenReturn(Lists.newArrayList(categoryCars));
    }

    @Test
    public void cars_trade_ad_should_return_url_except_carpopupEqualsFalse() {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(9311L);
        ad.setStatus(AdStatus.LIVE);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "trade");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.POST, new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE)), false));
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
        assertEquals("/thankyou/thekey?action=post&payment=insertion,feature&type=BUMP_UP,INSERTION&multiple=no&poptype=&advertId=10", url);
    }

    @Test
    public void cars_private_ad_should_return_url_except_carpopupEqualsTrue() {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(9311L);
        ad.setStatus(AdStatus.LIVE);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.POST, null, false));
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
        assertEquals("/thankyou/thekey?action=post&payment=none&type=BUMP_UP,INSERTION&multiple=no&poptype=carwow&advertId=10", url);
    }

    @Test
    public void cars_private_nonpost_ad_should_return_url_except_carpopupEqualsFalse() {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(9311L);
        ad.setStatus(AdStatus.LIVE);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.RELIST, new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE)), false));
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
        assertEquals("/thankyou/thekey?action=relist&payment=insertion,feature&type=BUMP_UP,INSERTION&multiple=no&poptype=&advertId=10", url);
    }

    @Test
    public void cars_subcategory_private_ad_should_return_url_except_carpopupEqualsTrue() {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(10432L);
        ad.setStatus(AdStatus.LIVE);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.POST, new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE)), false));
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
        assertEquals("/thankyou/thekey?action=post&payment=insertion,feature&type=BUMP_UP,INSERTION&multiple=no&poptype=carwow&advertId=10", url);
    }

    @Test
    public void noncars_private_ad_should_return_url_except_carpopupEqualsFalse() {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(-9311L);
        ad.setStatus(AdStatus.LIVE);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.POST, new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE)), false));
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
        assertEquals("/thankyou/thekey?action=post&payment=insertion,feature&type=BUMP_UP,INSERTION&multiple=no&poptype=&advertId=10", url);
    }

    @Test
    public void noncars_trade_ad_should_return_url_except_carpopupEqualsFalse() {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(-9311L);
        ad.setStatus(AdStatus.LIVE);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "trade");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.POST, new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE)), false));
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
        assertEquals("/thankyou/thekey?action=post&payment=insertion,feature&type=BUMP_UP,INSERTION&multiple=no&poptype=&advertId=10", url);
    }

    private CheckoutAdvert getCheckoutAdvert(Ad ad, String sellerType) {
        return CheckoutAdvert.Builder.builder()
                .categoryId(ad.getCategoryId())
                .locationId(ad.getLocationId())
                .sellerType(sellerType)
                .id(ad.getId()).build();
    }
}
