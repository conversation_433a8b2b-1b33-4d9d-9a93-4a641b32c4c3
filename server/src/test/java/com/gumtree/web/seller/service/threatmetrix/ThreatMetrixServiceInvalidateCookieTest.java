package com.gumtree.web.seller.service.threatmetrix;

import com.gumtree.common.properties.Env;
import com.gumtree.web.cookie.DefaultCookieResolver;
import com.gumtree.web.cookie.GumtreeCookieProperty;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookieCutter;
import com.netflix.config.ConfigurationManager;
import org.junit.Before;
import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Properties;
import java.util.UUID;

import static org.fest.assertions.api.Assertions.assertThat;

/**
 * 参考 MessageCentreCookieHelperTest 和 MadgexCookieHelperTest 的测试模式
 * 为 ThreatMetrixService.invalidateThreatMetrixCookie 方法编写单元测试
 */
public class ThreatMetrixServiceInvalidateCookieTest {

    static {
        Properties properties = new Properties();
        properties.setProperty("gumtree.env", Env.PROD.name());
        properties.setProperty(GumtreeCookieProperty.COOKIES_SECURE.getPropertyName(), "true");
        properties.setProperty(GumtreeCookieProperty.COOKIES_DOMAIN.getPropertyName(), "gumtree.com");
        ConfigurationManager.loadProperties(properties);
    }

    private ThreatMetrixService threatMetrixService;
    private static final String TEST_ORG_ID = "test-org-123";
    private static final String TEST_WEB_BASE_URL = "https://test.gumtree.com";

    @Before
    public void setUp() {
        DefaultCookieResolver cookieResolver = new DefaultCookieResolver();
        ThreatMetrixCookieCutter threatMetrixCookieCutter = new ThreatMetrixCookieCutter("gumtree.com");
        
        threatMetrixService = new ThreatMetrixService();
        ReflectionTestUtils.setField(threatMetrixService, "cookieResolver", cookieResolver);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", threatMetrixCookieCutter);
        ReflectionTestUtils.setField(threatMetrixService, "organisationId", TEST_ORG_ID);
        ReflectionTestUtils.setField(threatMetrixService, "webBaseUrl", TEST_WEB_BASE_URL);
        ReflectionTestUtils.setField(threatMetrixService, "enabled", true);
    }

    @Test
    public void testInvalidateThreatMetrixCookie_WhenDisabled() {
        // Given
        ReflectionTestUtils.setField(threatMetrixService, "enabled", false);
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        // When
        threatMetrixService.invalidateThreatMetrixCookie(request, response);

        // Then
        assertThat(response.getCookies()).isEmpty();
    }

    @Test
    public void testInvalidateThreatMetrixCookie_WhenCookieExists() {
        // Given
        String cookieName = "gt_tm"; // PROD环境下的cookie名称
        String domain = "gumtree.com";
        ServletRequest request = createRequestWithCookie(cookieName, domain);
        MockHttpServletResponse response = new MockHttpServletResponse();

        // When
        threatMetrixService.invalidateThreatMetrixCookie((HttpServletRequest) request, response);

        // Then - 验证过期cookie被添加
        Cookie expiredCookie = response.getCookie(cookieName);
        assertThat(expiredCookie).isNotNull();
        assertThat(expiredCookie.getMaxAge()).isEqualTo(0);
        assertThat(expiredCookie.getValue()).isEqualTo("");
        assertThat(expiredCookie.getPath()).isEqualTo("/");
        assertThat(expiredCookie.isHttpOnly()).isTrue();
        assertThat(expiredCookie.getSecure()).isTrue();
    }

    @Test
    public void testInvalidateThreatMetrixCookie_WhenCookieDoesNotExist() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        // When
        threatMetrixService.invalidateThreatMetrixCookie(request, response);

        // Then
        assertThat(response.getCookies()).isEmpty();
    }


    public void testInvalidateThreatMetrixCookie_WhenCookieExistsWithoutDomain() {
        // Given - host-only cookie (没有domain)
        String cookieName = "gt_tm";
        MockHttpServletRequest request = createRequestWithCookieNoDomain(cookieName);
        MockHttpServletResponse response = new MockHttpServletResponse();

        // When
        threatMetrixService.invalidateThreatMetrixCookie(request, response);

        // Then
        Cookie expiredCookie = response.getCookie(cookieName);
        assertThat(expiredCookie).isNotNull();
        assertThat(expiredCookie.getMaxAge()).isEqualTo(0);
    }

    @Test
    public void testInvalidateThreatMetrixCookie_WhenCookieNameDoesNotMatch() {
        // Given
        String wrongCookieName = "other_cookie";
        String domain = "gumtree.com";
        ServletRequest request = createRequestWithCookie(wrongCookieName, domain);
        MockHttpServletResponse response = new MockHttpServletResponse();

        // When
        threatMetrixService.invalidateThreatMetrixCookie((HttpServletRequest) request, response);

        // Then - 不应该添加任何过期cookie，因为cookie名称不匹配
        assertThat(response.getCookie("gt_tm")).isNull();
        assertThat(response.getCookie(wrongCookieName)).isNull();
    }

    @Test
    public void testInvalidateThreatMetrixCookie_WhenRequestHasNoCookies() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        // 不设置任何cookies
        MockHttpServletResponse response = new MockHttpServletResponse();

        // When
        threatMetrixService.invalidateThreatMetrixCookie(request, response);

        // Then
        assertThat(response.getCookies()).isEmpty();
    }

    @Test
    public void testInvalidateThreatMetrixCookie_VerifyExpiredCookieProperties() {
        // Given
        String cookieName = "gt_tm"; // PROD环境下的cookie名称
        String domain = "gumtree.com";
        ServletRequest request = createRequestWithCookie(cookieName,domain);
        ServletResponse response = aResponse();

        // When
        threatMetrixService.invalidateThreatMetrixCookie((HttpServletRequest) request, (HttpServletResponse)response);

        assertThat(((MockHttpServletResponse)response).getCookie("gt_tm")).isNotNull();
        assertThat(((MockHttpServletResponse)response).getCookie("gt_tm").getMaxAge()).isEqualTo(0);
    }

    // Helper methods

    private ServletRequest createRequestWithCookie(String cookieName, String domain) {
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookie = createCookie(cookieName, domain);
        request.setCookies(cookie);
        return request;
    }

    private MockHttpServletRequest createRequestWithCookieNoDomain(String cookieName) {
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookie = new Cookie(cookieName, UUID.randomUUID().toString());
        // 不设置domain，创建host-only cookie
        cookie.setMaxAge(3600);
        cookie.setPath("/");
        cookie.setSecure(true);
        cookie.setHttpOnly(true);
        request.setCookies(cookie);
        return request;
    }

    private Cookie createCookie(String cookieName, String domain) {
        Cookie cookie = new Cookie(cookieName, UUID.randomUUID().toString());
        cookie.setDomain(domain);
        cookie.setMaxAge(-1);
        cookie.setPath("/");
        cookie.setSecure(true);
        cookie.setHttpOnly(true);
        return cookie;
    }

    private ServletResponse aResponse() {
        return new MockHttpServletResponse();
    }

    private ServletRequest aRequest() {
        return new MockHttpServletRequest() {
        };
    }
}
